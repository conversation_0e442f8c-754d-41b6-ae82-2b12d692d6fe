/**
 * 通用工具函数库
 */

// 生成唯一ID
function generateId() {
  return 'id_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 深度克隆对象
function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime());
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item));
  }
  
  const cloned = {};
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloned[key] = deepClone(obj[key]);
    }
  }
  return cloned;
}

// 防抖函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 节流函数
function throttle(func, limit) {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// 验证邮箱格式
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 验证电话号码格式
function isValidPhone(phone) {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone.replace(/\D/g, ''));
}

// 格式化日期
function formatDate(date) {
  if (!date) return '';
  const d = new Date(date);
  return d.toLocaleDateString('zh-CN');
}

// 计算简历完整度
function calculateCompleteness(resumeData) {
  if (!resumeData) return 0;
  
  let totalFields = 0;
  let filledFields = 0;
  
  // 个人信息
  const personalInfo = resumeData.personalInfo || {};
  const personalFields = ['fullName', 'email', 'phone'];
  personalFields.forEach(field => {
    totalFields++;
    if (personalInfo[field] && personalInfo[field].trim()) {
      filledFields++;
    }
  });
  
  // 工作经验
  totalFields++;
  if (resumeData.workExperience && resumeData.workExperience.length > 0) {
    filledFields++;
  }
  
  // 教育背景
  totalFields++;
  if (resumeData.education && resumeData.education.length > 0) {
    filledFields++;
  }
  
  // 技能
  totalFields++;
  if (resumeData.skills && resumeData.skills.length > 0) {
    filledFields++;
  }
  
  return Math.round((filledFields / totalFields) * 100);
}

// 显示通知
function showNotification(message, type = 'info') {
  // 创建通知元素
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.textContent = message;
  
  // 添加样式
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 4px;
    color: white;
    font-size: 14px;
    z-index: 10000;
    max-width: 300px;
    word-wrap: break-word;
  `;
  
  // 根据类型设置背景色
  switch (type) {
    case 'success':
      notification.style.backgroundColor = '#4CAF50';
      break;
    case 'error':
      notification.style.backgroundColor = '#f44336';
      break;
    case 'warning':
      notification.style.backgroundColor = '#ff9800';
      break;
    default:
      notification.style.backgroundColor = '#2196F3';
  }
  
  document.body.appendChild(notification);
  
  // 3秒后自动移除
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 3000);
}

// 错误处理
function handleError(error, context = '') {
  console.error(`错误 ${context}:`, error);
  showNotification(`操作失败: ${error.message}`, 'error');
}

// 成功提示
function showSuccess(message) {
  showNotification(message, 'success');
}

// 警告提示
function showWarning(message) {
  showNotification(message, 'warning');
}

// 导出到全局
if (typeof window !== 'undefined') {
  window.Utils = {
    generateId,
    deepClone,
    debounce,
    throttle,
    isValidEmail,
    isValidPhone,
    formatDate,
    calculateCompleteness,
    showNotification,
    handleError,
    showSuccess,
    showWarning
  };
}
