// 这里应该包含mammoth.js的压缩版本
// 由于文件较大，这里提供一个简化的文档解析器
// 在实际项目中，您需要从 https://github.com/mwilliamson/mammoth.js 下载完整的mammoth.min.js

/**
 * 简化的文档解析器 - 用于演示目的
 * 在生产环境中请使用完整的mammoth.js库
 */
window.mammoth = {
  extractRawText: function(options) {
    return new Promise((resolve, reject) => {
      const file = options.arrayBuffer || options.buffer;
      
      if (!file) {
        reject(new Error('没有提供文件数据'));
        return;
      }
      
      // 这里是一个简化的实现
      // 实际的mammoth.js会正确解析DOC文件
      const reader = new FileReader();
      reader.onload = function(e) {
        try {
          // 简化的文本提取 - 实际应该解析DOC格式
          const text = "示例简历内容\n\n姓名: 张三\n邮箱: <EMAIL>\n电话: 13800138000\n地址: 北京市朝阳区\n\n工作经验:\n2020-2023 软件工程师 ABC公司\n负责前端开发工作\n\n教育背景:\n2016-2020 计算机科学与技术 北京大学\n\n技能:\nJavaScript, React, Node.js";
          
          resolve({ value: text });
        } catch (error) {
          reject(error);
        }
      };
      
      reader.onerror = function() {
        reject(new Error('文件读取失败'));
      };
      
      // 如果是ArrayBuffer，直接处理
      if (file instanceof ArrayBuffer) {
        reader.readAsText(new Blob([file]));
      } else {
        reader.readAsText(file);
      }
    });
  }
};
