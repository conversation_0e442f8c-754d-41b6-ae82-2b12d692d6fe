/**
 * Content Script 样式 - 用于高亮已填写的字段
 */

/* 已填写字段的高亮样式 */
.resume-autofill-highlighted {
  border: 2px solid #4CAF50 !important;
  background-color: rgba(76, 175, 80, 0.1) !important;
  box-shadow: 0 0 5px rgba(76, 175, 80, 0.3) !important;
  transition: all 0.3s ease !important;
}

/* 悬停效果 */
.resume-autofill-highlighted:hover {
  background-color: rgba(76, 175, 80, 0.2) !important;
}

/* 自动填写按钮样式 */
.resume-autofill-button {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
}

.resume-autofill-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.resume-autofill-button:active {
  transform: translateY(0);
}

/* 自动填写面板样式 */
.resume-autofill-panel {
  position: fixed;
  top: 70px;
  right: 20px;
  width: 350px;
  max-height: 500px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  z-index: 10001;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  border: 1px solid #e0e0e0;
}

.resume-autofill-panel-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px 20px;
  font-weight: 600;
  font-size: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.resume-autofill-panel-close {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.resume-autofill-panel-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.resume-autofill-panel-content {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.resume-autofill-resume-select {
  margin-bottom: 15px;
}

.resume-autofill-resume-select label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.resume-autofill-resume-select select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
  background: white;
}

.resume-autofill-field-list {
  margin-bottom: 15px;
}

.resume-autofill-field-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 13px;
}

.resume-autofill-field-item:last-child {
  border-bottom: none;
}

.resume-autofill-field-label {
  font-weight: 500;
  color: #333;
  flex: 1;
}

.resume-autofill-field-confidence {
  background: #e8f5e8;
  color: #2e7d32;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  margin-left: 10px;
}

.resume-autofill-field-confidence.low {
  background: #fff3e0;
  color: #f57c00;
}

.resume-autofill-field-confidence.medium {
  background: #e3f2fd;
  color: #1976d2;
}

.resume-autofill-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.resume-autofill-btn {
  flex: 1;
  padding: 10px 15px;
  border: none;
  border-radius: 5px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.resume-autofill-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.resume-autofill-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.resume-autofill-btn-secondary {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
}

.resume-autofill-btn-secondary:hover {
  background: #e8e8e8;
}

/* 通知样式 */
.resume-autofill-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 5px;
  color: white;
  font-size: 14px;
  z-index: 10002;
  max-width: 300px;
  word-wrap: break-word;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.resume-autofill-notification.success {
  background: linear-gradient(135deg, #4CAF50, #45a049);
}

.resume-autofill-notification.error {
  background: linear-gradient(135deg, #f44336, #d32f2f);
}

.resume-autofill-notification.warning {
  background: linear-gradient(135deg, #ff9800, #f57c00);
}

.resume-autofill-notification.info {
  background: linear-gradient(135deg, #2196F3, #1976d2);
}

/* 加载动画 */
.resume-autofill-loading {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: resume-autofill-spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes resume-autofill-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .resume-autofill-panel {
    width: 300px;
    right: 10px;
  }
  
  .resume-autofill-button {
    right: 10px;
  }
}

@media (max-width: 480px) {
  .resume-autofill-panel {
    width: calc(100vw - 20px);
    right: 10px;
    left: 10px;
  }
}
