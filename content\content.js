/**
 * Content Script - 在网页中检测表单并执行自动填写
 */

// 避免重复注入
if (!window.ResumeAutoFillInjected) {
  window.ResumeAutoFillInjected = true;

  class FormDetector {
    constructor() {
      this.fieldTypes = {
        NAME: 'name',
        EMAIL: 'email',
        PHONE: 'phone',
        ADDRESS: 'address',
        CITY: 'city',
        STATE: 'state',
        ZIPCODE: 'zipcode',
        COUNTRY: 'country',
        LINKEDIN: 'linkedin',
        WEBSITE: 'website',
        EXPERIENCE: 'experience',
        EDUCATION: 'education',
        SKILLS: 'skills',
        POSITION: 'position',
        COMPANY: 'company',
        SALARY: 'salary',
        COVER_LETTER: 'cover_letter',
        OTHER: 'other'
      };

      this.fieldPatterns = {
        [this.fieldTypes.NAME]: /name|姓名|全名|真实姓名|full.*name|first.*name|last.*name/i,
        [this.fieldTypes.EMAIL]: /email|邮箱|电子邮件|mail/i,
        [this.fieldTypes.PHONE]: /phone|电话|手机|mobile|tel|联系方式/i,
        [this.fieldTypes.ADDRESS]: /address|地址|住址|详细地址/i,
        [this.fieldTypes.CITY]: /city|城市|所在城市/i,
        [this.fieldTypes.STATE]: /state|省份|省|州/i,
        [this.fieldTypes.ZIPCODE]: /zip|postal|邮编|邮政编码/i,
        [this.fieldTypes.COUNTRY]: /country|国家|nationality|国籍/i,
        [this.fieldTypes.LINKEDIN]: /linkedin|领英/i,
        [this.fieldTypes.WEBSITE]: /website|网站|个人网站|homepage|blog/i,
        [this.fieldTypes.EXPERIENCE]: /experience|工作经验|工作经历|work.*history|employment/i,
        [this.fieldTypes.EDUCATION]: /education|教育|学历|educational|degree|university|college|school/i,
        [this.fieldTypes.SKILLS]: /skill|技能|专业技能|abilities|competencies/i,
        [this.fieldTypes.POSITION]: /position|职位|岗位|job.*title|role/i,
        [this.fieldTypes.COMPANY]: /company|公司|employer|organization|单位/i,
        [this.fieldTypes.SALARY]: /salary|薪资|薪水|wage|compensation|expected.*salary/i,
        [this.fieldTypes.COVER_LETTER]: /cover.*letter|求职信|自我介绍|personal.*statement/i
      };
    }

    /**
     * 检测页面中的所有表单
     */
    detectForms() {
      const forms = document.querySelectorAll('form');
      const formInfos = [];

      forms.forEach((form, index) => {
        const fields = this.analyzeFormFields(form);
        if (fields.length > 0) {
          formInfos.push({
            formIndex: index,
            element: form,
            fields: fields,
            isJobApplication: this.isJobApplicationForm(fields)
          });
        }
      });

      // 如果没有找到form标签，检测独立的输入字段
      if (formInfos.length === 0) {
        const independentFields = this.detectIndependentFields();
        if (independentFields.length > 0) {
          formInfos.push({
            formIndex: -1,
            element: document.body,
            fields: independentFields,
            isJobApplication: this.isJobApplicationForm(independentFields)
          });
        }
      }

      return formInfos;
    }

    /**
     * 分析表单字段
     */
    analyzeFormFields(form) {
      const fields = [];
      const inputs = form.querySelectorAll('input, textarea, select');

      inputs.forEach((element, index) => {
        const fieldInfo = this.analyzeField(element, index);
        if (fieldInfo) {
          fields.push(fieldInfo);
        }
      });

      return fields;
    }

    /**
     * 检测独立的输入字段（不在form标签内）
     */
    detectIndependentFields() {
      const fields = [];
      const inputs = document.querySelectorAll('input:not(form input), textarea:not(form textarea), select:not(form select)');

      inputs.forEach((element, index) => {
        const fieldInfo = this.analyzeField(element, index);
        if (fieldInfo && this.isRelevantField(fieldInfo)) {
          fields.push(fieldInfo);
        }
      });

      return fields;
    }

    /**
     * 分析单个字段
     */
    analyzeField(element, index) {
      // 跳过隐藏字段和特殊类型
      if (element.type === 'hidden' || 
          element.type === 'submit' || 
          element.type === 'button' ||
          element.style.display === 'none' ||
          element.offsetParent === null) {
        return null;
      }

      const fieldInfo = {
        element: element,
        index: index,
        type: this.identifyFieldType(element),
        label: this.getFieldLabel(element),
        placeholder: element.placeholder || '',
        name: element.name || '',
        id: element.id || '',
        required: element.required || element.hasAttribute('required'),
        value: element.value || '',
        confidence: 0
      };

      // 计算识别置信度
      fieldInfo.confidence = this.calculateConfidence(fieldInfo);

      return fieldInfo;
    }

    /**
     * 识别字段类型
     */
    identifyFieldType(element) {
      const text = [
        element.name,
        element.id,
        element.placeholder,
        this.getFieldLabel(element),
        element.className
      ].join(' ').toLowerCase();

      // 按优先级匹配字段类型
      for (const [type, pattern] of Object.entries(this.fieldPatterns)) {
        if (pattern.test(text)) {
          return type;
        }
      }

      // 根据input类型推断
      switch (element.type) {
        case 'email':
          return this.fieldTypes.EMAIL;
        case 'tel':
          return this.fieldTypes.PHONE;
        case 'url':
          return this.fieldTypes.WEBSITE;
        default:
          return this.fieldTypes.OTHER;
      }
    }

    /**
     * 获取字段标签
     */
    getFieldLabel(element) {
      // 查找关联的label
      let label = '';

      // 通过for属性关联的label
      if (element.id) {
        const labelElement = document.querySelector(`label[for="${element.id}"]`);
        if (labelElement) {
          label = labelElement.textContent.trim();
        }
      }

      // 父级label
      if (!label) {
        const parentLabel = element.closest('label');
        if (parentLabel) {
          label = parentLabel.textContent.replace(element.value, '').trim();
        }
      }

      // 前面的文本节点或元素
      if (!label) {
        const prevElement = element.previousElementSibling;
        if (prevElement && (prevElement.tagName === 'LABEL' || prevElement.tagName === 'SPAN')) {
          label = prevElement.textContent.trim();
        }
      }

      // 查找附近的文本
      if (!label) {
        const parent = element.parentElement;
        if (parent) {
          const textNodes = this.getTextNodesNear(element);
          if (textNodes.length > 0) {
            label = textNodes[0].trim();
          }
        }
      }

      return label;
    }

    /**
     * 获取元素附近的文本节点
     */
    getTextNodesNear(element) {
      const texts = [];
      const parent = element.parentElement;
      
      if (parent) {
        const walker = document.createTreeWalker(
          parent,
          NodeFilter.SHOW_TEXT,
          null,
          false
        );

        let node;
        while (node = walker.nextNode()) {
          const text = node.textContent.trim();
          if (text && text.length > 1 && text.length < 50) {
            texts.push(text);
          }
        }
      }

      return texts;
    }

    /**
     * 计算字段识别置信度
     */
    calculateConfidence(fieldInfo) {
      let confidence = 0;

      // 基于字段类型的基础分数
      if (fieldInfo.type !== this.fieldTypes.OTHER) {
        confidence += 50;
      }

      // 有明确标签加分
      if (fieldInfo.label) {
        confidence += 20;
      }

      // 有placeholder加分
      if (fieldInfo.placeholder) {
        confidence += 10;
      }

      // 必填字段加分
      if (fieldInfo.required) {
        confidence += 10;
      }

      // 有name或id属性加分
      if (fieldInfo.name || fieldInfo.id) {
        confidence += 10;
      }

      return Math.min(confidence, 100);
    }

    /**
     * 判断是否为求职申请表单
     */
    isJobApplicationForm(fields) {
      const jobRelatedTypes = [
        this.fieldTypes.EXPERIENCE,
        this.fieldTypes.EDUCATION,
        this.fieldTypes.SKILLS,
        this.fieldTypes.POSITION,
        this.fieldTypes.COVER_LETTER
      ];

      const jobFieldCount = fields.filter(field => 
        jobRelatedTypes.includes(field.type)
      ).length;

      const personalFieldCount = fields.filter(field => 
        [this.fieldTypes.NAME, this.fieldTypes.EMAIL, this.fieldTypes.PHONE].includes(field.type)
      ).length;

      return jobFieldCount > 0 || personalFieldCount >= 2;
    }

    /**
     * 判断字段是否相关
     */
    isRelevantField(fieldInfo) {
      return fieldInfo.confidence > 30 || 
             fieldInfo.type !== this.fieldTypes.OTHER ||
             fieldInfo.required;
    }

    /**
     * 将字段映射到简历数据
     */
    mapFieldsToResumeData(fields, resumeData) {
      const mappings = [];

      fields.forEach(field => {
        const mapping = this.createFieldMapping(field, resumeData);
        if (mapping) {
          mappings.push(mapping);
        }
      });

      return mappings;
    }

    /**
     * 创建字段映射
     */
    createFieldMapping(field, resumeData) {
      const personalInfo = resumeData.personalInfo || {};
      const workExp = resumeData.workExperience || [];
      const education = resumeData.education || [];

      let value = '';
      let confidence = field.confidence;

      switch (field.type) {
        case this.fieldTypes.NAME:
          value = personalInfo.fullName || '';
          break;
        case this.fieldTypes.EMAIL:
          value = personalInfo.email || '';
          break;
        case this.fieldTypes.PHONE:
          value = personalInfo.phone || '';
          break;
        case this.fieldTypes.ADDRESS:
          value = personalInfo.address ? personalInfo.address.full || '' : '';
          break;
        case this.fieldTypes.CITY:
          value = personalInfo.address ? personalInfo.address.city || '' : '';
          break;
        case this.fieldTypes.LINKEDIN:
          value = personalInfo.linkedIn || '';
          break;
        case this.fieldTypes.WEBSITE:
          value = personalInfo.website || '';
          break;
        case this.fieldTypes.SKILLS:
          value = resumeData.skills ? resumeData.skills.join(', ') : '';
          break;
        case this.fieldTypes.EXPERIENCE:
          value = this.formatWorkExperience(workExp);
          break;
        case this.fieldTypes.EDUCATION:
          value = this.formatEducation(education);
          break;
        case this.fieldTypes.POSITION:
          value = workExp.length > 0 ? workExp[0].position : '';
          break;
        case this.fieldTypes.COMPANY:
          value = workExp.length > 0 ? workExp[0].company : '';
          break;
        default:
          return null;
      }

      if (!value) {
        confidence = Math.max(0, confidence - 30);
      }

      return {
        field: field,
        value: value,
        confidence: confidence,
        type: field.type
      };
    }

    /**
     * 格式化工作经验
     */
    formatWorkExperience(workExp) {
      return workExp.map(job => {
        const startDate = job.startDate ? new Date(job.startDate).getFullYear() : '';
        const endDate = job.endDate ? new Date(job.endDate).getFullYear() : '至今';
        return `${startDate}-${endDate} ${job.position} ${job.company}\n${job.description || ''}`;
      }).join('\n\n');
    }

    /**
     * 格式化教育背景
     */
    formatEducation(education) {
      return education.map(edu => {
        const gradYear = edu.graduationDate ? new Date(edu.graduationDate).getFullYear() : '';
        return `${gradYear} ${edu.degree || ''} ${edu.major || ''} ${edu.institution || ''}`;
      }).join('\n');
    }
  }

  class AutoFillEngine {
    constructor() {
      this.filledFields = [];
      this.highlightClass = 'resume-autofill-highlighted';
    }

    /**
     * 执行表单填写
     */
    fillForm(resumeData, mappings) {
      const results = {
        success: true,
        filledFields: 0,
        errors: [],
        warnings: []
      };

      // 清除之前的高亮
      this.clearHighlights();
      this.filledFields = [];

      mappings.forEach((mapping, index) => {
        try {
          if (mapping.value && mapping.field.element) {
            this.fillField(mapping.field.element, mapping.value);
            this.filledFields.push(mapping.field.element);
            results.filledFields++;
          } else if (!mapping.value) {
            results.warnings.push(`字段 "${mapping.field.label}" 没有对应的简历数据`);
          }
        } catch (error) {
          results.errors.push(`填写字段 "${mapping.field.label}" 失败: ${error.message}`);
          console.error('填写字段失败:', error);
        }
      });

      // 高亮已填写的字段
      this.highlightFilledFields();

      if (results.errors.length > 0) {
        results.success = false;
      }

      return results;
    }

    /**
     * 填写单个字段
     */
    fillField(element, value) {
      if (!element || !value) return;

      // 触发focus事件
      element.focus();

      // 根据元素类型填写
      switch (element.tagName.toLowerCase()) {
        case 'input':
          this.fillInputField(element, value);
          break;
        case 'textarea':
          this.fillTextareaField(element, value);
          break;
        case 'select':
          this.fillSelectField(element, value);
          break;
      }

      // 触发change和input事件
      this.triggerEvents(element);
    }

    /**
     * 填写input字段
     */
    fillInputField(element, value) {
      switch (element.type) {
        case 'checkbox':
          element.checked = Boolean(value);
          break;
        case 'radio':
          if (element.value === value) {
            element.checked = true;
          }
          break;
        default:
          element.value = value;
      }
    }

    /**
     * 填写textarea字段
     */
    fillTextareaField(element, value) {
      element.value = value;
    }

    /**
     * 填写select字段
     */
    fillSelectField(element, value) {
      // 尝试精确匹配
      for (let option of element.options) {
        if (option.value === value || option.text === value) {
          option.selected = true;
          return;
        }
      }

      // 尝试模糊匹配
      const lowerValue = value.toLowerCase();
      for (let option of element.options) {
        if (option.value.toLowerCase().includes(lowerValue) ||
            option.text.toLowerCase().includes(lowerValue)) {
          option.selected = true;
          return;
        }
      }
    }

    /**
     * 触发相关事件
     */
    triggerEvents(element) {
      const events = ['input', 'change', 'blur'];
      events.forEach(eventType => {
        const event = new Event(eventType, { bubbles: true });
        element.dispatchEvent(event);
      });
    }

    /**
     * 高亮已填写的字段
     */
    highlightFilledFields() {
      this.filledFields.forEach(element => {
        element.classList.add(this.highlightClass);
      });
    }

    /**
     * 清除高亮
     */
    clearHighlights() {
      const highlighted = document.querySelectorAll(`.${this.highlightClass}`);
      highlighted.forEach(element => {
        element.classList.remove(this.highlightClass);
      });
    }

    /**
     * 预览填写效果
     */
    previewFill(resumeData, mappings) {
      const preview = mappings.map(mapping => ({
        fieldLabel: mapping.field.label,
        fieldType: mapping.field.type,
        currentValue: mapping.field.element.value,
        newValue: mapping.value,
        confidence: mapping.confidence
      }));

      return preview;
    }

    /**
     * 撤销填写
     */
    undoFill() {
      this.filledFields.forEach(element => {
        element.value = '';
        element.classList.remove(this.highlightClass);
      });
      this.filledFields = [];
    }
  }

  // 创建全局实例
  window.ResumeAutoFill = {
    formDetector: new FormDetector(),
    autoFillEngine: new AutoFillEngine(),

    detectForms() {
      return this.formDetector.detectForms();
    },

    fillForm(resumeData, mappings) {
      return this.autoFillEngine.fillForm(resumeData, mappings);
    },

    mapFieldsToResumeData(fields, resumeData) {
      return this.formDetector.mapFieldsToResumeData(fields, resumeData);
    },

    previewFill(resumeData, mappings) {
      return this.autoFillEngine.previewFill(resumeData, mappings);
    },

    undoFill() {
      return this.autoFillEngine.undoFill();
    }
  };

  // UI控制器
  class UIController {
    constructor() {
      this.panel = null;
      this.button = null;
      this.isVisible = false;
      this.currentForms = [];
      this.currentResumes = [];
      this.selectedResumeId = null;
    }

    /**
     * 初始化UI
     */
    init() {
      this.createFloatingButton();
      this.loadResumes();
    }

    /**
     * 创建浮动按钮
     */
    createFloatingButton() {
      if (this.button) return;

      this.button = document.createElement('button');
      this.button.className = 'resume-autofill-button';
      this.button.textContent = '简历填写';
      this.button.onclick = () => this.togglePanel();

      document.body.appendChild(this.button);
    }

    /**
     * 切换面板显示
     */
    togglePanel() {
      if (this.isVisible) {
        this.hidePanel();
      } else {
        this.showPanel();
      }
    }

    /**
     * 显示面板
     */
    async showPanel() {
      if (!this.panel) {
        await this.createPanel();
      }

      this.panel.style.display = 'block';
      this.isVisible = true;

      // 检测表单
      this.detectAndDisplayForms();
    }

    /**
     * 隐藏面板
     */
    hidePanel() {
      if (this.panel) {
        this.panel.style.display = 'none';
      }
      this.isVisible = false;
    }

    /**
     * 创建面板
     */
    async createPanel() {
      this.panel = document.createElement('div');
      this.panel.className = 'resume-autofill-panel';
      this.panel.style.display = 'none';

      this.panel.innerHTML = `
        <div class="resume-autofill-panel-header">
          <span>简历自动填写</span>
          <button class="resume-autofill-panel-close">×</button>
        </div>
        <div class="resume-autofill-panel-content">
          <div class="resume-autofill-resume-select">
            <label>选择简历:</label>
            <select id="resume-select">
              <option value="">请选择简历...</option>
            </select>
          </div>
          <div class="resume-autofill-field-list" id="field-list">
            <p>正在检测表单字段...</p>
          </div>
          <div class="resume-autofill-actions">
            <button class="resume-autofill-btn resume-autofill-btn-primary" id="fill-btn">
              自动填写
            </button>
            <button class="resume-autofill-btn resume-autofill-btn-secondary" id="clear-btn">
              清除填写
            </button>
          </div>
        </div>
      `;

      // 绑定事件
      this.panel.querySelector('.resume-autofill-panel-close').onclick = () => this.hidePanel();
      this.panel.querySelector('#fill-btn').onclick = () => this.performAutoFill();
      this.panel.querySelector('#clear-btn').onclick = () => this.clearFill();
      this.panel.querySelector('#resume-select').onchange = (e) => this.onResumeSelect(e.target.value);

      document.body.appendChild(this.panel);
    }

    /**
     * 加载简历列表
     */
    async loadResumes() {
      try {
        const response = await chrome.runtime.sendMessage({ action: 'getResumes' });
        if (response.success) {
          this.currentResumes = Object.values(response.data);
          this.updateResumeSelect();
        }
      } catch (error) {
        console.error('加载简历失败:', error);
      }
    }

    /**
     * 更新简历选择下拉框
     */
    updateResumeSelect() {
      const select = document.querySelector('#resume-select');
      if (!select) return;

      select.innerHTML = '<option value="">请选择简历...</option>';

      this.currentResumes.forEach(resume => {
        const option = document.createElement('option');
        option.value = resume.id;
        option.textContent = `${resume.name} (${resume.completeness}%)`;
        select.appendChild(option);
      });
    }

    /**
     * 检测并显示表单
     */
    detectAndDisplayForms() {
      this.currentForms = window.ResumeAutoFill.detectForms();
      this.updateFieldList();
    }

    /**
     * 更新字段列表显示
     */
    updateFieldList() {
      const fieldList = document.querySelector('#field-list');
      if (!fieldList) return;

      if (this.currentForms.length === 0) {
        fieldList.innerHTML = '<p>未检测到可填写的表单字段</p>';
        return;
      }

      let html = '<h4>检测到的字段:</h4>';

      this.currentForms.forEach(form => {
        form.fields.forEach(field => {
          const confidenceClass = field.confidence > 70 ? 'high' :
                                 field.confidence > 40 ? 'medium' : 'low';

          html += `
            <div class="resume-autofill-field-item">
              <span class="resume-autofill-field-label">${field.label || field.name || '未知字段'}</span>
              <span class="resume-autofill-field-confidence ${confidenceClass}">${field.confidence}%</span>
            </div>
          `;
        });
      });

      fieldList.innerHTML = html;
    }

    /**
     * 简历选择事件
     */
    onResumeSelect(resumeId) {
      this.selectedResumeId = resumeId;
    }

    /**
     * 执行自动填写
     */
    async performAutoFill() {
      if (!this.selectedResumeId) {
        this.showNotification('请先选择一个简历', 'warning');
        return;
      }

      if (this.currentForms.length === 0) {
        this.showNotification('未检测到可填写的表单', 'warning');
        return;
      }

      try {
        // 获取选中的简历数据
        const selectedResume = this.currentResumes.find(r => r.id === this.selectedResumeId);
        if (!selectedResume) {
          this.showNotification('简历数据不存在', 'error');
          return;
        }

        // 创建字段映射
        const allFields = this.currentForms.flatMap(form => form.fields);
        const mappings = window.ResumeAutoFill.mapFieldsToResumeData(allFields, selectedResume.data);

        // 执行填写
        const result = window.ResumeAutoFill.fillForm(selectedResume.data, mappings);

        if (result.success) {
          this.showNotification(`成功填写 ${result.filledFields} 个字段`, 'success');
          if (result.warnings.length > 0) {
            console.warn('填写警告:', result.warnings);
          }
        } else {
          this.showNotification('填写过程中出现错误', 'error');
          console.error('填写错误:', result.errors);
        }

      } catch (error) {
        console.error('自动填写失败:', error);
        this.showNotification('自动填写失败', 'error');
      }
    }

    /**
     * 清除填写
     */
    clearFill() {
      window.ResumeAutoFill.undoFill();
      this.showNotification('已清除填写内容', 'info');
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
      const notification = document.createElement('div');
      notification.className = `resume-autofill-notification ${type}`;
      notification.textContent = message;

      document.body.appendChild(notification);

      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 3000);
    }

    /**
     * 销毁UI
     */
    destroy() {
      if (this.button) {
        this.button.remove();
        this.button = null;
      }

      if (this.panel) {
        this.panel.remove();
        this.panel = null;
      }

      this.isVisible = false;
    }
  }

  // 创建UI控制器实例
  const uiController = new UIController();

  // 页面加载完成后初始化UI
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => uiController.init());
  } else {
    uiController.init();
  }

  // 添加到全局对象
  window.ResumeAutoFill.uiController = uiController;

  console.log('简历自动填写助手 Content Script 已加载');
}
