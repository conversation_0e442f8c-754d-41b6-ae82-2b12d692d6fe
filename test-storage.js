/**
 * 存储测试脚本
 */

let resumeManager;

function log(message) {
  const logElement = document.getElementById('log-content');
  const timestamp = new Date().toLocaleTimeString();
  logElement.textContent += `[${timestamp}] ${message}\n`;
  console.log(message);
}

async function createTestResume() {
  try {
    log('开始创建测试简历...');
    
    const testResumeData = {
      personalInfo: {
        fullName: '测试用户',
        email: '<EMAIL>',
        phone: '13800138000',
        address: {
          full: '北京市朝阳区',
          city: '北京',
          country: '中国'
        }
      },
      workExperience: [{
        company: '测试公司',
        position: '软件工程师',
        startDate: '2020-01-01',
        endDate: '2023-12-31',
        description: '负责前端开发工作'
      }],
      education: [{
        institution: '测试大学',
        degree: '学士',
        major: '计算机科学',
        graduationDate: '2020-06-30'
      }],
      skills: ['JavaScript', 'HTML', 'CSS']
    };
    
    const resumeId = await resumeManager.createResume(testResumeData, '测试简历');
    log(`测试简历创建成功，ID: ${resumeId}`);
    
    // 自动加载最新数据
    await loadAllResumes();
    
  } catch (error) {
    log(`创建测试简历失败: ${error.message}`);
  }
}

async function loadAllResumes() {
  try {
    log('开始加载所有简历...');
    
    const resumes = await resumeManager.getAllResumes();
    const resumeList = await resumeManager.getResumeList();
    
    log(`找到 ${Object.keys(resumes).length} 个简历`);
    log(`简历列表包含 ${resumeList.length} 项`);
    
    document.getElementById('storage-content').textContent = JSON.stringify({
      rawData: resumes,
      resumeList: resumeList
    }, null, 2);
    
  } catch (error) {
    log(`加载简历失败: ${error.message}`);
  }
}

async function clearAllData() {
  try {
    log('开始清除所有数据...');
    
    await resumeManager.clearAllData();
    log('所有数据已清除');
    
    document.getElementById('storage-content').textContent = '数据已清除';
    
  } catch (error) {
    log(`清除数据失败: ${error.message}`);
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  // 初始化简历管理器
  resumeManager = new ResumeManager();
  
  // 绑定按钮事件
  document.getElementById('create-test-btn').addEventListener('click', createTestResume);
  document.getElementById('load-resumes-btn').addEventListener('click', loadAllResumes);
  document.getElementById('clear-data-btn').addEventListener('click', clearAllData);
  
  // 监听存储变化
  chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'local') {
      log(`存储发生变化: ${Object.keys(changes).join(', ')}`);
      if (changes.resumes) {
        log('简历数据已更新');
      }
    }
  });
  
  log('页面加载完成');
  loadAllResumes();
});
