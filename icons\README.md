# 图标文件说明

## 图标要求

Microsoft Edge扩展需要以下尺寸的图标文件：

- `icon16.png` - 16x16像素（工具栏小图标）
- `icon32.png` - 32x32像素（扩展管理页面）
- `icon48.png` - 48x48像素（扩展详情页面）
- `icon128.png` - 128x128像素（Chrome网上应用店）

## 当前状态

目前提供了一个SVG格式的图标文件 `icon.svg`，您需要将其转换为所需的PNG格式。

## 转换方法

### 方法一：在线转换工具
1. 访问在线SVG转PNG工具（如 convertio.co）
2. 上传 `icon.svg` 文件
3. 设置输出尺寸为所需大小
4. 下载转换后的PNG文件

### 方法二：使用设计软件
1. 在Adobe Illustrator、Inkscape或其他矢量图形软件中打开SVG文件
2. 导出为PNG格式，设置相应尺寸
3. 保存为对应的文件名

### 方法三：使用命令行工具
如果安装了ImageMagick：
```bash
# 转换为不同尺寸
convert icon.svg -resize 16x16 icon16.png
convert icon.svg -resize 32x32 icon32.png
convert icon.svg -resize 48x48 icon48.png
convert icon.svg -resize 128x128 icon128.png
```

## 图标设计说明

当前图标设计包含：
- 渐变背景（蓝紫色）
- 文档图标（白色）
- 自动填写标识（绿色圆圈带魔法棒）

设计理念：
- 简洁明了，易于识别
- 体现文档处理和自动化功能
- 符合现代扁平化设计风格

## 注意事项

1. **文件命名**：确保PNG文件名与manifest.json中的配置一致
2. **图像质量**：使用高质量的PNG格式，避免失真
3. **背景透明**：建议使用透明背景，适应不同主题
4. **视觉一致性**：所有尺寸的图标应保持视觉一致性

## 临时解决方案

如果暂时无法生成PNG文件，可以：
1. 使用浏览器打开SVG文件
2. 截图并调整为所需尺寸
3. 保存为PNG格式

这是一个临时方案，建议尽快使用专业工具生成高质量的图标文件。
