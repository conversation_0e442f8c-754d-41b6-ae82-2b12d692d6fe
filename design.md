# 设计文档

## 概述

简历自动填写扩展是一个Microsoft Edge浏览器扩展，旨在简化求职申请流程。该扩展采用模块化架构，包含文档解析、数据管理、表单检测、自动填写和可选的AI增强功能。扩展将使用Manifest V3规范，确保与现代浏览器安全标准的兼容性。

## 架构

### 整体架构图

```mermaid
graph TB
    A[用户界面层] --> B[业务逻辑层]
    B --> C[数据访问层]
    B --> D[内容脚本层]
    B --> E[AI服务层]
    
    subgraph "用户界面层"
        A1[弹出窗口UI]
        A2[选项页面]
        A3[内容脚本UI]
    end
    
    subgraph "业务逻辑层"
        B1[简历管理器]
        B2[文档解析器]
        B3[表单检测器]
        B4[自动填写引擎]
    end
    
    subgraph "数据访问层"
        C1[本地存储]
        C2[文件处理]
    end
    
    subgraph "内容脚本层"
        D1[DOM操作]
        D2[表单分析]
    end
    
    subgraph "AI服务层"
        E1[字段识别AI]
        E2[内容匹配AI]
    end
```

### 扩展组件结构

- **Background Script**: 管理扩展生命周期和跨标签页通信
- **Content Scripts**: 注入到网页中进行DOM操作和表单检测
- **Popup**: 主要用户界面，用于快速操作
- **Options Page**: 详细设置和简历管理界面
- **Storage**: 使用Chrome Storage API管理数据持久化

## 组件和接口

### 1. 文档解析组件 (DocumentParser)

**职责**: 解析上传的DOC文件并提取简历信息

**接口**:
```typescript
interface DocumentParser {
  parseDocument(file: File): Promise<ResumeData>
  validateDocument(file: File): boolean
  getSupportedFormats(): string[]
}

interface ResumeData {
  personalInfo: PersonalInfo
  workExperience: WorkExperience[]
  education: Education[]
  skills: string[]
  metadata: DocumentMetadata
}
```

**实现方式**: 
- 使用mammoth.js库解析DOC文件
- 实现文本模式识别算法提取结构化数据
- 支持多种简历格式的模板匹配

### 2. 简历管理组件 (ResumeManager)

**职责**: 管理多个简历档案的CRUD操作

**接口**:
```typescript
interface ResumeManager {
  createResume(data: ResumeData): Promise<string>
  updateResume(id: string, data: Partial<ResumeData>): Promise<void>
  deleteResume(id: string): Promise<void>
  getResume(id: string): Promise<ResumeData>
  getAllResumes(): Promise<ResumeProfile[]>
  validateResume(data: ResumeData): ValidationResult
}

interface ResumeProfile {
  id: string
  name: string
  lastModified: Date
  completeness: number
  data: ResumeData
}
```

### 3. 表单检测组件 (FormDetector)

**职责**: 检测和分析网页上的表单字段

**接口**:
```typescript
interface FormDetector {
  detectForms(): FormInfo[]
  analyzeField(element: HTMLElement): FieldInfo
  mapFieldsToResumeData(fields: FieldInfo[]): FieldMapping[]
}

interface FieldInfo {
  element: HTMLElement
  type: FieldType
  label: string
  placeholder: string
  required: boolean
  confidence: number
}

enum FieldType {
  NAME = 'name',
  EMAIL = 'email',
  PHONE = 'phone',
  ADDRESS = 'address',
  EXPERIENCE = 'experience',
  EDUCATION = 'education',
  SKILLS = 'skills'
}
```

### 4. 自动填写引擎 (AutoFillEngine)

**职责**: 执行表单自动填写操作

**接口**:
```typescript
interface AutoFillEngine {
  fillForm(resumeId: string, mappings: FieldMapping[]): Promise<FillResult>
  previewFill(resumeId: string, mappings: FieldMapping[]): FillPreview
  undoFill(): void
}

interface FillResult {
  success: boolean
  filledFields: number
  errors: FillError[]
  warnings: string[]
}
```

### 5. AI服务组件 (AIService)

**职责**: 提供AI增强的字段识别和内容匹配

**接口**:
```typescript
interface AIService {
  identifyFields(pageContent: string): Promise<AIFieldAnalysis>
  matchContent(fields: FieldInfo[], resumeData: ResumeData): Promise<ContentMatch[]>
  configure(apiKey: string, endpoint: string): void
  isEnabled(): boolean
}

interface AIFieldAnalysis {
  fields: EnhancedFieldInfo[]
  confidence: number
  suggestions: string[]
}
```

## 数据模型

### 简历数据结构

```typescript
interface PersonalInfo {
  fullName: string
  email: string
  phone: string
  address: Address
  linkedIn?: string
  website?: string
}

interface Address {
  street: string
  city: string
  state: string
  zipCode: string
  country: string
}

interface WorkExperience {
  company: string
  position: string
  startDate: Date
  endDate?: Date
  description: string
  achievements: string[]
}

interface Education {
  institution: string
  degree: string
  major: string
  graduationDate: Date
  gpa?: number
}
```

### 存储架构

```typescript
interface StorageSchema {
  resumes: { [id: string]: ResumeProfile }
  settings: UserSettings
  fieldMappings: { [domain: string]: FieldMapping[] }
  aiConfig: AIConfiguration
}

interface UserSettings {
  defaultResumeId?: string
  autoFillEnabled: boolean
  aiEnabled: boolean
  highlightFilledFields: boolean
}
```

## 错误处理

### 错误分类和处理策略

1. **文档解析错误**
   - 文件格式不支持: 显示支持格式列表
   - 文件损坏: 提示重新上传
   - 解析失败: 提供手动输入选项

2. **网络和API错误**
   - AI服务不可用: 降级到基本功能
   - 存储配额超限: 提示清理旧数据
   - 权限错误: 引导用户重新授权

3. **表单填写错误**
   - 字段不可访问: 跳过并记录
   - 数据格式不匹配: 尝试格式转换
   - 页面结构变化: 重新检测表单

### 错误恢复机制

```typescript
interface ErrorHandler {
  handleError(error: ExtensionError): ErrorResponse
  retry(operation: () => Promise<any>, maxAttempts: number): Promise<any>
  fallback(primaryOperation: () => Promise<any>, fallbackOperation: () => Promise<any>): Promise<any>
}
```

## 测试策略

### 单元测试
- 文档解析功能测试
- 数据验证逻辑测试
- 表单字段检测算法测试
- AI服务集成测试

### 集成测试
- 端到端自动填写流程测试
- 多简历管理功能测试
- 跨浏览器兼容性测试

### 用户接受测试
- 常见求职网站兼容性测试
- 不同简历格式解析准确性测试
- AI功能准确性和性能测试

### 测试环境配置

```typescript
interface TestConfig {
  mockAIService: boolean
  testDataPath: string
  targetWebsites: string[]
  performanceThresholds: {
    parseTime: number
    fillTime: number
    memoryUsage: number
  }
}
```

## 安全考虑

### 数据保护
- 所有简历数据仅存储在本地
- 敏感信息加密存储
- AI API调用时数据脱敏处理

### 权限管理
- 最小权限原则
- 明确的权限请求说明
- 用户可控的功能开关

### 隐私保护
- 不收集用户个人信息
- AI功能可完全禁用
- 透明的数据使用政策