/**
 * AI服务 - 提供AI增强的字段识别和内容匹配
 */

class AIService {
  constructor() {
    this.apiKey = null;
    this.endpoint = null;
    this.enabled = false;
    this.maxRetries = 3;
    this.timeout = 10000; // 10秒超时
  }

  /**
   * 配置AI服务
   */
  configure(config) {
    this.apiKey = config.apiKey;
    this.endpoint = config.endpoint;
    this.enabled = !!(this.apiKey && this.endpoint);
  }

  /**
   * 检查AI服务是否启用
   */
  isEnabled() {
    return this.enabled && this.apiKey && this.endpoint;
  }

  /**
   * 测试AI服务连接
   */
  async testConnection() {
    if (!this.isEnabled()) {
      throw new Error('AI服务未配置');
    }

    try {
      const response = await this.makeRequest('/test', {
        method: 'GET'
      });

      return {
        success: true,
        message: 'AI服务连接正常',
        latency: response.latency
      };
    } catch (error) {
      throw new Error(`AI服务连接失败: ${error.message}`);
    }
  }

  /**
   * AI字段识别
   */
  async identifyFields(pageContent, existingFields = []) {
    if (!this.isEnabled()) {
      // 降级到基本识别
      return this.basicFieldIdentification(existingFields);
    }

    try {
      const prompt = this.buildFieldIdentificationPrompt(pageContent, existingFields);
      
      const response = await this.makeRequest('/analyze-fields', {
        method: 'POST',
        body: JSON.stringify({
          prompt: prompt,
          page_content: pageContent,
          existing_fields: existingFields.map(field => ({
            label: field.label,
            name: field.name,
            type: field.type,
            placeholder: field.placeholder
          }))
        })
      });

      return this.processFieldAnalysisResponse(response, existingFields);
    } catch (error) {
      console.warn('AI字段识别失败，降级到基本识别:', error);
      return this.basicFieldIdentification(existingFields);
    }
  }

  /**
   * AI内容匹配
   */
  async matchContent(fields, resumeData) {
    if (!this.isEnabled()) {
      // 降级到基本匹配
      return this.basicContentMatching(fields, resumeData);
    }

    try {
      const prompt = this.buildContentMatchingPrompt(fields, resumeData);
      
      const response = await this.makeRequest('/match-content', {
        method: 'POST',
        body: JSON.stringify({
          prompt: prompt,
          fields: fields.map(field => ({
            label: field.label,
            name: field.name,
            type: field.type,
            placeholder: field.placeholder,
            required: field.required
          })),
          resume_data: this.sanitizeResumeData(resumeData)
        })
      });

      return this.processContentMatchResponse(response, fields, resumeData);
    } catch (error) {
      console.warn('AI内容匹配失败，降级到基本匹配:', error);
      return this.basicContentMatching(fields, resumeData);
    }
  }

  /**
   * 构建字段识别提示
   */
  buildFieldIdentificationPrompt(pageContent, fields) {
    return `
请分析以下网页内容，识别表单字段的用途和类型：

网页内容摘要：
${pageContent.substring(0, 2000)}

已检测到的字段：
${fields.map(field => `- ${field.label || field.name}: ${field.type}`).join('\n')}

请为每个字段提供：
1. 字段用途分类（姓名、邮箱、电话、地址、工作经验等）
2. 置信度评分（0-100）
3. 改进建议

请以JSON格式返回结果。
    `.trim();
  }

  /**
   * 构建内容匹配提示
   */
  buildContentMatchingPrompt(fields, resumeData) {
    const personalInfo = resumeData.personalInfo || {};
    const workExp = resumeData.workExperience || [];
    const education = resumeData.education || [];

    return `
请将以下简历数据匹配到表单字段：

简历信息：
- 姓名: ${personalInfo.fullName || '未提供'}
- 邮箱: ${personalInfo.email || '未提供'}
- 电话: ${personalInfo.phone || '未提供'}
- 地址: ${personalInfo.address?.full || '未提供'}
- 工作经验: ${workExp.length} 项
- 教育背景: ${education.length} 项
- 技能: ${resumeData.skills?.join(', ') || '未提供'}

表单字段：
${fields.map(field => `- ${field.label || field.name} (${field.type})`).join('\n')}

请为每个字段提供最佳匹配的内容和置信度。
    `.trim();
  }

  /**
   * 发起AI请求
   */
  async makeRequest(endpoint, options = {}) {
    const url = `${this.endpoint}${endpoint}`;
    
    const requestOptions = {
      method: options.method || 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        ...options.headers
      },
      body: options.body,
      signal: AbortSignal.timeout(this.timeout)
    };

    let lastError;
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const startTime = Date.now();
        const response = await fetch(url, requestOptions);
        const latency = Date.now() - startTime;

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        return { ...data, latency };
      } catch (error) {
        lastError = error;
        
        if (attempt < this.maxRetries) {
          // 指数退避
          const delay = Math.pow(2, attempt) * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError;
  }

  /**
   * 处理字段分析响应
   */
  processFieldAnalysisResponse(response, originalFields) {
    try {
      const analysis = response.analysis || response;
      
      return {
        fields: originalFields.map((field, index) => {
          const aiAnalysis = analysis.fields?.[index] || {};
          
          return {
            ...field,
            aiType: aiAnalysis.type || field.type,
            aiConfidence: aiAnalysis.confidence || field.confidence,
            aiSuggestions: aiAnalysis.suggestions || []
          };
        }),
        confidence: analysis.overall_confidence || 75,
        suggestions: analysis.suggestions || []
      };
    } catch (error) {
      console.error('处理AI字段分析响应失败:', error);
      return this.basicFieldIdentification(originalFields);
    }
  }

  /**
   * 处理内容匹配响应
   */
  processContentMatchResponse(response, fields, resumeData) {
    try {
      const matches = response.matches || response;
      
      return fields.map((field, index) => {
        const aiMatch = matches[index] || {};
        
        return {
          field: field,
          value: aiMatch.value || this.getBasicFieldValue(field, resumeData),
          confidence: aiMatch.confidence || 50,
          reasoning: aiMatch.reasoning || '',
          aiEnhanced: true
        };
      });
    } catch (error) {
      console.error('处理AI内容匹配响应失败:', error);
      return this.basicContentMatching(fields, resumeData);
    }
  }

  /**
   * 基本字段识别（降级方案）
   */
  basicFieldIdentification(fields) {
    return {
      fields: fields.map(field => ({
        ...field,
        aiType: field.type,
        aiConfidence: field.confidence,
        aiSuggestions: []
      })),
      confidence: 60,
      suggestions: ['AI服务不可用，使用基本识别算法']
    };
  }

  /**
   * 基本内容匹配（降级方案）
   */
  basicContentMatching(fields, resumeData) {
    return fields.map(field => ({
      field: field,
      value: this.getBasicFieldValue(field, resumeData),
      confidence: 60,
      reasoning: '基本匹配算法',
      aiEnhanced: false
    }));
  }

  /**
   * 获取基本字段值
   */
  getBasicFieldValue(field, resumeData) {
    const personalInfo = resumeData.personalInfo || {};
    const workExp = resumeData.workExperience || [];
    const education = resumeData.education || [];

    switch (field.type) {
      case 'name':
        return personalInfo.fullName || '';
      case 'email':
        return personalInfo.email || '';
      case 'phone':
        return personalInfo.phone || '';
      case 'address':
        return personalInfo.address?.full || '';
      case 'city':
        return personalInfo.address?.city || '';
      case 'linkedin':
        return personalInfo.linkedIn || '';
      case 'website':
        return personalInfo.website || '';
      case 'skills':
        return resumeData.skills?.join(', ') || '';
      case 'experience':
        return this.formatWorkExperience(workExp);
      case 'education':
        return this.formatEducation(education);
      case 'position':
        return workExp.length > 0 ? workExp[0].position : '';
      case 'company':
        return workExp.length > 0 ? workExp[0].company : '';
      default:
        return '';
    }
  }

  /**
   * 格式化工作经验
   */
  formatWorkExperience(workExp) {
    return workExp.map(job => {
      const startDate = job.startDate ? new Date(job.startDate).getFullYear() : '';
      const endDate = job.endDate ? new Date(job.endDate).getFullYear() : '至今';
      return `${startDate}-${endDate} ${job.position} ${job.company}`;
    }).join('\n');
  }

  /**
   * 格式化教育背景
   */
  formatEducation(education) {
    return education.map(edu => {
      const gradYear = edu.graduationDate ? new Date(edu.graduationDate).getFullYear() : '';
      return `${gradYear} ${edu.degree || ''} ${edu.major || ''} ${edu.institution || ''}`;
    }).join('\n');
  }

  /**
   * 清理简历数据（移除敏感信息）
   */
  sanitizeResumeData(resumeData) {
    const sanitized = Utils.deepClone(resumeData);
    
    // 可以在这里移除或脱敏敏感信息
    // 例如：部分隐藏电话号码、邮箱等
    
    return sanitized;
  }

  /**
   * 获取AI服务状态
   */
  getStatus() {
    return {
      enabled: this.enabled,
      configured: !!(this.apiKey && this.endpoint),
      apiKey: this.apiKey ? `${this.apiKey.substring(0, 8)}...` : null,
      endpoint: this.endpoint
    };
  }

  /**
   * 重置AI服务配置
   */
  reset() {
    this.apiKey = null;
    this.endpoint = null;
    this.enabled = false;
  }
}

// 导出到全局
if (typeof window !== 'undefined') {
  window.AIService = AIService;
}
