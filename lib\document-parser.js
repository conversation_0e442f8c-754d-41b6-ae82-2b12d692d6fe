/**
 * 文档解析器 - 解析DOC文件并提取简历信息
 */

class DocumentParser {
  constructor() {
    this.supportedFormats = ['.doc', '.docx'];
  }

  /**
   * 验证文件格式
   */
  validateDocument(file) {
    if (!file) {
      throw new Error('请选择文件');
    }

    const fileName = file.name.toLowerCase();
    const isSupported = this.supportedFormats.some(format => 
      fileName.endsWith(format)
    );

    if (!isSupported) {
      throw new Error(`不支持的文件格式。支持的格式: ${this.supportedFormats.join(', ')}`);
    }

    // 检查文件大小 (限制为5MB)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      throw new Error('文件大小不能超过5MB');
    }

    return true;
  }

  /**
   * 解析文档
   */
  async parseDocument(file) {
    try {
      this.validateDocument(file);

      // 读取文件为ArrayBuffer
      const arrayBuffer = await this.fileToArrayBuffer(file);
      
      // 使用mammoth.js提取文本
      const result = await mammoth.extractRawText({ arrayBuffer });
      const text = result.value;

      // 解析文本内容为结构化数据
      const resumeData = this.parseTextToResumeData(text);
      
      // 添加元数据
      resumeData.metadata = {
        fileName: file.name,
        fileSize: file.size,
        uploadDate: new Date().toISOString(),
        parsedDate: new Date().toISOString()
      };

      return resumeData;
    } catch (error) {
      console.error('文档解析失败:', error);
      throw error;
    }
  }

  /**
   * 将文件转换为ArrayBuffer
   */
  fileToArrayBuffer(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = () => reject(new Error('文件读取失败'));
      reader.readAsArrayBuffer(file);
    });
  }

  /**
   * 解析文本内容为结构化简历数据
   */
  parseTextToResumeData(text) {
    const resumeData = {
      personalInfo: {},
      workExperience: [],
      education: [],
      skills: [],
      metadata: {}
    };

    // 按行分割文本
    const lines = text.split('\n').map(line => line.trim()).filter(line => line);

    // 解析个人信息
    this.parsePersonalInfo(lines, resumeData);
    
    // 解析工作经验
    this.parseWorkExperience(lines, resumeData);
    
    // 解析教育背景
    this.parseEducation(lines, resumeData);
    
    // 解析技能
    this.parseSkills(lines, resumeData);

    return resumeData;
  }

  /**
   * 解析个人信息
   */
  parsePersonalInfo(lines, resumeData) {
    const personalInfo = {};

    lines.forEach(line => {
      // 姓名匹配
      if (line.includes('姓名') || line.includes('Name')) {
        const nameMatch = line.match(/(?:姓名|Name)[：:]\s*(.+)/i);
        if (nameMatch) {
          personalInfo.fullName = nameMatch[1].trim();
        }
      }

      // 邮箱匹配
      const emailMatch = line.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
      if (emailMatch) {
        personalInfo.email = emailMatch[1];
      }

      // 电话匹配
      const phoneMatch = line.match(/(?:电话|手机|Phone|Mobile)[：:]\s*([1-9]\d{10}|\d{3}-\d{4}-\d{4}|\d{3}\s\d{4}\s\d{4})/i);
      if (phoneMatch) {
        personalInfo.phone = phoneMatch[1].replace(/\D/g, '');
      }

      // 地址匹配
      if (line.includes('地址') || line.includes('Address')) {
        const addressMatch = line.match(/(?:地址|Address)[：:]\s*(.+)/i);
        if (addressMatch) {
          personalInfo.address = {
            full: addressMatch[1].trim(),
            city: this.extractCity(addressMatch[1]),
            country: '中国'
          };
        }
      }

      // LinkedIn匹配
      if (line.includes('linkedin') || line.includes('LinkedIn')) {
        const linkedinMatch = line.match(/(https?:\/\/(?:www\.)?linkedin\.com\/in\/[^\s]+)/i);
        if (linkedinMatch) {
          personalInfo.linkedIn = linkedinMatch[1];
        }
      }
    });

    resumeData.personalInfo = personalInfo;
  }

  /**
   * 解析工作经验
   */
  parseWorkExperience(lines, resumeData) {
    const workExperience = [];
    let currentJob = null;
    let inWorkSection = false;

    lines.forEach((line, index) => {
      // 检测工作经验章节开始
      if (line.match(/工作经验|工作履历|Work Experience|Employment/i)) {
        inWorkSection = true;
        return;
      }

      // 检测其他章节开始，结束工作经验解析
      if (inWorkSection && line.match(/教育背景|教育经历|技能|Education|Skills/i)) {
        inWorkSection = false;
        if (currentJob) {
          workExperience.push(currentJob);
          currentJob = null;
        }
        return;
      }

      if (inWorkSection) {
        // 匹配工作时间和公司
        const jobMatch = line.match(/(\d{4}[-年]\d{1,2}|\d{4})\s*[-至到~]\s*(\d{4}[-年]\d{1,2}|\d{4}|至今|现在|Present)\s+(.+?)\s+(.+)/);
        if (jobMatch) {
          if (currentJob) {
            workExperience.push(currentJob);
          }
          
          currentJob = {
            startDate: this.parseDate(jobMatch[1]),
            endDate: jobMatch[2].includes('至今') || jobMatch[2].includes('现在') || jobMatch[2].includes('Present') 
              ? null : this.parseDate(jobMatch[2]),
            position: jobMatch[3].trim(),
            company: jobMatch[4].trim(),
            description: '',
            achievements: []
          };
        } else if (currentJob && line.length > 10) {
          // 添加工作描述
          if (line.startsWith('•') || line.startsWith('-') || line.startsWith('*')) {
            currentJob.achievements.push(line.substring(1).trim());
          } else {
            currentJob.description += (currentJob.description ? '\n' : '') + line;
          }
        }
      }
    });

    if (currentJob) {
      workExperience.push(currentJob);
    }

    resumeData.workExperience = workExperience;
  }

  /**
   * 解析教育背景
   */
  parseEducation(lines, resumeData) {
    const education = [];
    let inEducationSection = false;

    lines.forEach(line => {
      // 检测教育背景章节
      if (line.match(/教育背景|教育经历|Education/i)) {
        inEducationSection = true;
        return;
      }

      if (line.match(/工作经验|技能|Skills|Work/i)) {
        inEducationSection = false;
        return;
      }

      if (inEducationSection) {
        // 匹配教育信息
        const eduMatch = line.match(/(\d{4})\s*[-年]\s*(\d{4})\s+(.+?)\s+(.+)/);
        if (eduMatch) {
          education.push({
            startDate: this.parseDate(eduMatch[1]),
            graduationDate: this.parseDate(eduMatch[2]),
            major: eduMatch[3].trim(),
            institution: eduMatch[4].trim(),
            degree: this.extractDegree(line)
          });
        }
      }
    });

    resumeData.education = education;
  }

  /**
   * 解析技能
   */
  parseSkills(lines, resumeData) {
    const skills = [];
    let inSkillsSection = false;

    lines.forEach(line => {
      if (line.match(/技能|Skills|专业技能/i)) {
        inSkillsSection = true;
        return;
      }

      if (inSkillsSection && line.length > 0) {
        // 分割技能
        const skillList = line.split(/[,，、;；\s]+/).filter(skill => skill.trim());
        skills.push(...skillList);
      }
    });

    resumeData.skills = [...new Set(skills)]; // 去重
  }

  /**
   * 解析日期
   */
  parseDate(dateStr) {
    if (!dateStr) return null;
    
    // 处理中文年月格式
    dateStr = dateStr.replace(/年/g, '-').replace(/月/g, '');
    
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? dateStr : date.toISOString();
  }

  /**
   * 提取城市信息
   */
  extractCity(address) {
    const cityMatch = address.match(/(北京|上海|广州|深圳|杭州|南京|武汉|成都|西安|重庆|天津|青岛|大连|厦门|苏州|无锡|宁波|长沙|郑州|济南|福州|合肥|昆明|南昌|太原|石家庄|哈尔滨|长春|沈阳|乌鲁木齐|兰州|银川|西宁|拉萨|呼和浩特|南宁|海口|贵阳)/);
    return cityMatch ? cityMatch[1] : '';
  }

  /**
   * 提取学位信息
   */
  extractDegree(text) {
    const degreeMatch = text.match(/(博士|硕士|学士|本科|专科|PhD|Master|Bachelor)/i);
    return degreeMatch ? degreeMatch[1] : '';
  }

  /**
   * 获取支持的文件格式
   */
  getSupportedFormats() {
    return this.supportedFormats;
  }
}

// 导出到全局
if (typeof window !== 'undefined') {
  window.DocumentParser = DocumentParser;
}
