/**
 * 错误处理器 - 统一的错误处理和恢复机制
 */

class ErrorHandler {
  constructor() {
    this.errorLog = [];
    this.maxLogSize = 100;
    this.retryDelays = [1000, 2000, 4000]; // 重试延迟（毫秒）
  }

  /**
   * 处理错误
   */
  handleError(error, context = '', options = {}) {
    const errorInfo = this.createErrorInfo(error, context);
    this.logError(errorInfo);

    // 根据错误类型决定处理策略
    const response = this.determineErrorResponse(errorInfo, options);
    
    // 如果需要显示给用户
    if (options.showToUser !== false) {
      this.showUserError(errorInfo, response);
    }

    return response;
  }

  /**
   * 创建错误信息对象
   */
  createErrorInfo(error, context) {
    return {
      id: this.generateErrorId(),
      timestamp: new Date().toISOString(),
      context: context,
      type: this.classifyError(error),
      message: error.message || String(error),
      stack: error.stack,
      userAgent: navigator.userAgent,
      url: window.location?.href || 'extension',
      severity: this.determineSeverity(error)
    };
  }

  /**
   * 错误分类
   */
  classifyError(error) {
    if (error.name === 'NetworkError' || error.message.includes('fetch')) {
      return 'NETWORK_ERROR';
    }
    if (error.name === 'TypeError' && error.message.includes('Cannot read')) {
      return 'NULL_REFERENCE_ERROR';
    }
    if (error.name === 'SyntaxError') {
      return 'SYNTAX_ERROR';
    }
    if (error.message.includes('storage')) {
      return 'STORAGE_ERROR';
    }
    if (error.message.includes('permission')) {
      return 'PERMISSION_ERROR';
    }
    if (error.message.includes('file') || error.message.includes('document')) {
      return 'FILE_ERROR';
    }
    if (error.message.includes('AI') || error.message.includes('API')) {
      return 'AI_SERVICE_ERROR';
    }
    return 'UNKNOWN_ERROR';
  }

  /**
   * 确定错误严重程度
   */
  determineSeverity(error) {
    const criticalKeywords = ['storage', 'permission', 'security'];
    const warningKeywords = ['network', 'timeout', 'AI'];
    
    const message = error.message.toLowerCase();
    
    if (criticalKeywords.some(keyword => message.includes(keyword))) {
      return 'CRITICAL';
    }
    if (warningKeywords.some(keyword => message.includes(keyword))) {
      return 'WARNING';
    }
    return 'INFO';
  }

  /**
   * 决定错误响应策略
   */
  determineErrorResponse(errorInfo, options) {
    const response = {
      success: false,
      error: errorInfo,
      action: 'SHOW_ERROR',
      retryable: false,
      fallbackAvailable: false
    };

    switch (errorInfo.type) {
      case 'NETWORK_ERROR':
        response.action = 'RETRY_WITH_FALLBACK';
        response.retryable = true;
        response.fallbackAvailable = true;
        response.userMessage = '网络连接失败，请检查网络设置后重试';
        break;

      case 'STORAGE_ERROR':
        response.action = 'CLEAR_STORAGE_PROMPT';
        response.userMessage = '存储空间不足或数据损坏，建议清理数据';
        break;

      case 'PERMISSION_ERROR':
        response.action = 'REQUEST_PERMISSION';
        response.userMessage = '需要相应权限才能继续操作';
        break;

      case 'FILE_ERROR':
        response.action = 'SHOW_FILE_HELP';
        response.userMessage = '文件格式不支持或文件损坏，请检查文件';
        break;

      case 'AI_SERVICE_ERROR':
        response.action = 'FALLBACK_TO_BASIC';
        response.fallbackAvailable = true;
        response.userMessage = 'AI服务暂时不可用，已切换到基本模式';
        break;

      case 'NULL_REFERENCE_ERROR':
        response.action = 'RELOAD_DATA';
        response.userMessage = '数据加载异常，正在重新加载';
        break;

      default:
        response.userMessage = '操作失败，请稍后重试';
    }

    return response;
  }

  /**
   * 重试操作
   */
  async retry(operation, maxAttempts = 3, context = '') {
    let lastError;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const result = await operation();
        
        // 如果之前有错误，记录恢复成功
        if (lastError) {
          this.logRecovery(context, attempt);
        }
        
        return result;
      } catch (error) {
        lastError = error;
        
        if (attempt < maxAttempts) {
          const delay = this.retryDelays[Math.min(attempt - 1, this.retryDelays.length - 1)];
          await this.delay(delay);
        }
      }
    }

    // 所有重试都失败了
    throw this.enhanceError(lastError, `重试 ${maxAttempts} 次后仍然失败`);
  }

  /**
   * 降级操作
   */
  async fallback(primaryOperation, fallbackOperation, context = '') {
    try {
      return await primaryOperation();
    } catch (primaryError) {
      console.warn(`主要操作失败，切换到降级方案: ${context}`, primaryError);
      
      try {
        const result = await fallbackOperation();
        this.logFallback(context, primaryError);
        return result;
      } catch (fallbackError) {
        // 降级方案也失败了
        throw this.enhanceError(fallbackError, `主要操作和降级方案都失败了`);
      }
    }
  }

  /**
   * 验证器
   */
  validate(data, schema, context = '') {
    const errors = [];
    
    try {
      this.validateObject(data, schema, '', errors);
      
      if (errors.length > 0) {
        throw new ValidationError(`验证失败: ${errors.join(', ')}`, errors);
      }
      
      return { valid: true, errors: [] };
    } catch (error) {
      const errorInfo = this.createErrorInfo(error, `验证失败: ${context}`);
      this.logError(errorInfo);
      
      return {
        valid: false,
        errors: error.validationErrors || [error.message]
      };
    }
  }

  /**
   * 验证对象
   */
  validateObject(data, schema, path, errors) {
    if (!schema) return;

    for (const [key, rules] of Object.entries(schema)) {
      const value = data?.[key];
      const fieldPath = path ? `${path}.${key}` : key;

      if (rules.required && (value === undefined || value === null || value === '')) {
        errors.push(`${fieldPath} 是必填字段`);
        continue;
      }

      if (value !== undefined && value !== null) {
        this.validateField(value, rules, fieldPath, errors);
      }
    }
  }

  /**
   * 验证字段
   */
  validateField(value, rules, path, errors) {
    // 类型验证
    if (rules.type && typeof value !== rules.type) {
      errors.push(`${path} 类型错误，期望 ${rules.type}，实际 ${typeof value}`);
    }

    // 长度验证
    if (rules.minLength && value.length < rules.minLength) {
      errors.push(`${path} 长度不能少于 ${rules.minLength} 个字符`);
    }
    if (rules.maxLength && value.length > rules.maxLength) {
      errors.push(`${path} 长度不能超过 ${rules.maxLength} 个字符`);
    }

    // 格式验证
    if (rules.pattern && !rules.pattern.test(value)) {
      errors.push(`${path} 格式不正确`);
    }

    // 自定义验证
    if (rules.validator && !rules.validator(value)) {
      errors.push(`${path} 验证失败`);
    }

    // 嵌套对象验证
    if (rules.properties && typeof value === 'object') {
      this.validateObject(value, rules.properties, path, errors);
    }

    // 数组验证
    if (rules.items && Array.isArray(value)) {
      value.forEach((item, index) => {
        this.validateField(item, rules.items, `${path}[${index}]`, errors);
      });
    }
  }

  /**
   * 显示用户错误
   */
  showUserError(errorInfo, response) {
    const message = response.userMessage || '操作失败，请稍后重试';
    
    if (typeof Utils !== 'undefined' && Utils.showNotification) {
      const type = errorInfo.severity === 'CRITICAL' ? 'error' : 'warning';
      Utils.showNotification(message, type);
    } else {
      console.error('用户错误:', message);
    }
  }

  /**
   * 记录错误
   */
  logError(errorInfo) {
    this.errorLog.unshift(errorInfo);
    
    // 限制日志大小
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize);
    }

    // 输出到控制台
    console.error(`[${errorInfo.type}] ${errorInfo.context}:`, errorInfo.message);
    
    // 严重错误额外处理
    if (errorInfo.severity === 'CRITICAL') {
      this.handleCriticalError(errorInfo);
    }
  }

  /**
   * 记录恢复成功
   */
  logRecovery(context, attempts) {
    console.info(`操作恢复成功: ${context} (尝试 ${attempts} 次)`);
  }

  /**
   * 记录降级操作
   */
  logFallback(context, originalError) {
    console.warn(`降级操作: ${context}`, originalError.message);
  }

  /**
   * 处理严重错误
   */
  handleCriticalError(errorInfo) {
    // 可以在这里添加严重错误的特殊处理
    // 例如：发送错误报告、清理损坏数据等
    console.error('严重错误:', errorInfo);
  }

  /**
   * 增强错误信息
   */
  enhanceError(error, additionalInfo) {
    const enhanced = new Error(`${error.message} (${additionalInfo})`);
    enhanced.originalError = error;
    enhanced.stack = error.stack;
    return enhanced;
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 生成错误ID
   */
  generateErrorId() {
    return 'err_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 获取错误日志
   */
  getErrorLog() {
    return [...this.errorLog];
  }

  /**
   * 清除错误日志
   */
  clearErrorLog() {
    this.errorLog = [];
  }

  /**
   * 导出错误日志
   */
  exportErrorLog() {
    return JSON.stringify({
      errors: this.errorLog,
      exportDate: new Date().toISOString(),
      userAgent: navigator.userAgent
    }, null, 2);
  }
}

/**
 * 验证错误类
 */
class ValidationError extends Error {
  constructor(message, validationErrors = []) {
    super(message);
    this.name = 'ValidationError';
    this.validationErrors = validationErrors;
  }
}

// 创建全局错误处理器实例
const globalErrorHandler = new ErrorHandler();

// 全局错误捕获
window.addEventListener('error', (event) => {
  globalErrorHandler.handleError(event.error, '全局错误', { showToUser: false });
});

window.addEventListener('unhandledrejection', (event) => {
  globalErrorHandler.handleError(event.reason, '未处理的Promise拒绝', { showToUser: false });
});

// 导出到全局
if (typeof window !== 'undefined') {
  window.ErrorHandler = ErrorHandler;
  window.ValidationError = ValidationError;
  window.globalErrorHandler = globalErrorHandler;
}
