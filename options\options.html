<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>简历自动填写助手 - 设置</title>
  <link rel="stylesheet" href="options.css">
</head>
<body>
  <div class="options-container">
    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <h1>简历助手</h1>
        <p>设置与管理</p>
      </div>
      
      <nav class="sidebar-nav">
        <a href="#resumes" class="nav-item active" data-tab="resumes">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
          </svg>
          简历管理
        </a>
        
        <a href="#settings" class="nav-item" data-tab="settings">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z"/>
          </svg>
          基本设置
        </a>
        
        <a href="#ai" class="nav-item" data-tab="ai">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
          </svg>
          AI设置
        </a>
        
        <a href="#about" class="nav-item" data-tab="about">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V11H11V17Z"/>
          </svg>
          关于
        </a>
      </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 简历管理标签页 -->
      <div id="resumes-tab" class="tab-content active">
        <div class="tab-header">
          <h2>简历管理</h2>
          <div class="tab-actions">
            <button id="upload-resume-btn" class="btn btn-primary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
              </svg>
              上传简历
            </button>
            <input type="file" id="resume-file-input" accept=".doc,.docx" style="display: none;">
          </div>
        </div>

        <div class="resume-grid" id="resume-grid">
          <div class="loading-placeholder">加载中...</div>
        </div>
      </div>

      <!-- 基本设置标签页 -->
      <div id="settings-tab" class="tab-content">
        <div class="tab-header">
          <h2>基本设置</h2>
        </div>

        <div class="settings-section">
          <div class="setting-group">
            <h3>自动填写设置</h3>
            
            <div class="setting-item">
              <label class="setting-label">
                <input type="checkbox" id="auto-fill-enabled">
                <span class="checkmark"></span>
                启用自动填写功能
              </label>
              <p class="setting-description">允许扩展自动检测和填写表单字段</p>
            </div>

            <div class="setting-item">
              <label class="setting-label">
                <input type="checkbox" id="highlight-fields">
                <span class="checkmark"></span>
                高亮已填写字段
              </label>
              <p class="setting-description">填写完成后高亮显示已填写的字段</p>
            </div>

            <div class="setting-item">
              <label for="default-resume">默认简历:</label>
              <select id="default-resume" class="setting-select">
                <option value="">请选择默认简历...</option>
              </select>
              <p class="setting-description">设置默认使用的简历档案</p>
            </div>
          </div>

          <div class="setting-group">
            <h3>数据管理</h3>
            
            <div class="setting-item">
              <button id="export-data-btn" class="btn btn-secondary">导出所有数据</button>
              <p class="setting-description">导出所有简历和设置数据</p>
            </div>

            <div class="setting-item">
              <button id="import-data-btn" class="btn btn-secondary">导入数据</button>
              <input type="file" id="import-file-input" accept=".json" style="display: none;">
              <p class="setting-description">从备份文件导入数据</p>
            </div>

            <div class="setting-item">
              <button id="clear-data-btn" class="btn btn-danger">清除所有数据</button>
              <p class="setting-description">删除所有简历和设置（不可恢复）</p>
            </div>
          </div>
        </div>
      </div>

      <!-- AI设置标签页 -->
      <div id="ai-tab" class="tab-content">
        <div class="tab-header">
          <h2>AI设置</h2>
        </div>

        <div class="settings-section">
          <div class="setting-group">
            <h3>AI功能</h3>
            
            <div class="setting-item">
              <label class="setting-label">
                <input type="checkbox" id="ai-enabled">
                <span class="checkmark"></span>
                启用AI增强功能
              </label>
              <p class="setting-description">使用AI提高字段识别和内容匹配的准确性</p>
            </div>

            <div class="ai-config" id="ai-config" style="display: none;">
              <div class="setting-item">
                <label for="ai-api-key">API密钥:</label>
                <input type="password" id="ai-api-key" class="setting-input" placeholder="请输入您的AI服务API密钥">
                <p class="setting-description">用于访问AI服务的API密钥</p>
              </div>

              <div class="setting-item">
                <label for="ai-endpoint">API端点:</label>
                <input type="url" id="ai-endpoint" class="setting-input" placeholder="https://api.example.com/v1">
                <p class="setting-description">AI服务的API端点地址</p>
              </div>

              <div class="setting-item">
                <button id="test-ai-btn" class="btn btn-secondary">测试连接</button>
                <p class="setting-description">测试AI服务连接是否正常</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 关于标签页 -->
      <div id="about-tab" class="tab-content">
        <div class="tab-header">
          <h2>关于</h2>
        </div>

        <div class="about-section">
          <div class="about-info">
            <h3>简历自动填写助手</h3>
            <p class="version">版本 1.0.0</p>
            <p class="description">
              智能简历自动填写扩展，支持DOC文件解析和AI增强功能。
              帮助您快速、准确地填写求职申请表单，节省时间并减少错误。
            </p>
          </div>

          <div class="feature-list">
            <h4>主要功能</h4>
            <ul>
              <li>支持DOC/DOCX格式简历解析</li>
              <li>智能表单字段检测</li>
              <li>多简历档案管理</li>
              <li>自动填写功能</li>
              <li>AI增强的字段识别</li>
              <li>数据本地存储，保护隐私</li>
            </ul>
          </div>

          <div class="support-info">
            <h4>技术支持</h4>
            <p>如果您在使用过程中遇到问题，请联系技术支持。</p>
            <div class="support-links">
              <a href="#" class="support-link">使用帮助</a>
              <a href="#" class="support-link">反馈问题</a>
              <a href="#" class="support-link">隐私政策</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 通知容器 -->
  <div id="notification-container" class="notification-container"></div>

  <!-- 确认对话框 -->
  <div id="confirm-dialog" class="modal" style="display: none;">
    <div class="modal-content">
      <h3 id="confirm-title">确认操作</h3>
      <p id="confirm-message">您确定要执行此操作吗？</p>
      <div class="modal-actions">
        <button id="confirm-cancel" class="btn btn-secondary">取消</button>
        <button id="confirm-ok" class="btn btn-danger">确认</button>
      </div>
    </div>
  </div>

  <!-- 脚本 -->
  <script src="../lib/utils.js"></script>
  <script src="../lib/mammoth.min.js"></script>
  <script src="../lib/document-parser.js"></script>
  <script src="../lib/resume-manager.js"></script>
  <script src="options.js"></script>
</body>
</html>
