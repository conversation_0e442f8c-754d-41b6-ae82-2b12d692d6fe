/**
 * 背景脚本 - 管理扩展生命周期和跨标签页通信
 */

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener(async (details) => {
  console.log('简历自动填写助手已安装');
  
  // 初始化默认设置
  const defaultSettings = {
    autoFillEnabled: true,
    aiEnabled: false,
    highlightFilledFields: true,
    defaultResumeId: null
  };
  
  // 检查是否已有设置，如果没有则设置默认值
  const result = await chrome.storage.local.get(['settings']);
  if (!result.settings) {
    await chrome.storage.local.set({ settings: defaultSettings });
  }
});

// 处理来自content script和popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('收到消息:', request);
  
  switch (request.action) {
    case 'getResumes':
      handleGetResumes(sendResponse);
      break;
    case 'saveResume':
      handleSaveResume(request.data, sendResponse);
      break;
    case 'deleteResume':
      handleDeleteResume(request.resumeId, sendResponse);
      break;
    case 'getSettings':
      handleGetSettings(sendResponse);
      break;
    case 'saveSettings':
      handleSaveSettings(request.settings, sendResponse);
      break;
    case 'detectForms':
      handleDetectForms(sender.tab.id, sendResponse);
      break;
    case 'fillForm':
      handleFillForm(sender.tab.id, request.resumeId, request.mappings, sendResponse);
      break;
    default:
      console.warn('未知的消息类型:', request.action);
      sendResponse({ success: false, error: '未知的消息类型' });
  }
  
  // 返回true表示异步响应
  return true;
});

// 获取所有简历
async function handleGetResumes(sendResponse) {
  try {
    const result = await chrome.storage.local.get(['resumes']);
    const resumes = result.resumes || {};
    sendResponse({ success: true, data: resumes });
  } catch (error) {
    console.error('获取简历失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 保存简历
async function handleSaveResume(resumeData, sendResponse) {
  try {
    const result = await chrome.storage.local.get(['resumes']);
    const resumes = result.resumes || {};
    
    const resumeId = resumeData.id || generateUniqueId();
    resumes[resumeId] = {
      ...resumeData,
      id: resumeId,
      lastModified: new Date().toISOString()
    };
    
    await chrome.storage.local.set({ resumes });
    sendResponse({ success: true, resumeId });
  } catch (error) {
    console.error('保存简历失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 删除简历
async function handleDeleteResume(resumeId, sendResponse) {
  try {
    const result = await chrome.storage.local.get(['resumes']);
    const resumes = result.resumes || {};
    
    if (resumes[resumeId]) {
      delete resumes[resumeId];
      await chrome.storage.local.set({ resumes });
      sendResponse({ success: true });
    } else {
      sendResponse({ success: false, error: '简历不存在' });
    }
  } catch (error) {
    console.error('删除简历失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 获取设置
async function handleGetSettings(sendResponse) {
  try {
    const result = await chrome.storage.local.get(['settings']);
    sendResponse({ success: true, data: result.settings || {} });
  } catch (error) {
    console.error('获取设置失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 保存设置
async function handleSaveSettings(settings, sendResponse) {
  try {
    await chrome.storage.local.set({ settings });
    sendResponse({ success: true });
  } catch (error) {
    console.error('保存设置失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 检测表单
async function handleDetectForms(tabId, sendResponse) {
  try {
    const results = await chrome.scripting.executeScript({
      target: { tabId },
      function: detectFormsInPage
    });
    
    sendResponse({ success: true, data: results[0].result });
  } catch (error) {
    console.error('检测表单失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 填写表单
async function handleFillForm(tabId, resumeId, mappings, sendResponse) {
  try {
    // 获取简历数据
    const result = await chrome.storage.local.get(['resumes']);
    const resumes = result.resumes || {};
    const resumeData = resumes[resumeId];
    
    if (!resumeData) {
      throw new Error('简历不存在');
    }
    
    // 在页面中执行填写操作
    const results = await chrome.scripting.executeScript({
      target: { tabId },
      function: fillFormInPage,
      args: [resumeData, mappings]
    });
    
    sendResponse({ success: true, data: results[0].result });
  } catch (error) {
    console.error('填写表单失败:', error);
    sendResponse({ success: false, error: error.message });
  }
}

// 生成唯一ID
function generateUniqueId() {
  return 'resume_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 注入到页面中的表单检测函数
function detectFormsInPage() {
  // 这个函数将在content script中实现
  return window.ResumeAutoFill ? window.ResumeAutoFill.detectForms() : [];
}

// 注入到页面中的表单填写函数
function fillFormInPage(resumeData, mappings) {
  // 这个函数将在content script中实现
  return window.ResumeAutoFill ? window.ResumeAutoFill.fillForm(resumeData, mappings) : { success: false };
}
