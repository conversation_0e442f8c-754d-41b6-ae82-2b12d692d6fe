<svg width="315" height="446" viewBox="0 0 315 446" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="157.5" cy="223" r="150" fill="url(#gradient1)" opacity="0.3"/>
    <circle cx="100" cy="150" r="80" fill="url(#gradient2)" opacity="0.2"/>
    <circle cx="215" cy="300" r="60" fill="url(#gradient3)" opacity="0.25"/>
    <defs>
        <radialGradient id="gradient1" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(157.5 223) rotate(90) scale(150)">
            <stop stop-color="#E3D4FD"/>
            <stop offset="1" stop-color="#F1E9FF" stop-opacity="0"/>
        </radialGradient>
        <radialGradient id="gradient2" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(100 150) rotate(90) scale(80)">
            <stop stop-color="#D4B5FD"/>
            <stop offset="1" stop-color="#EDE4FF" stop-opacity="0"/>
        </radialGradient>
        <radialGradient id="gradient3" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(215 300) rotate(90) scale(60)">
            <stop stop-color="#C8A8FD"/>
            <stop offset="1" stop-color="#F8F4FF" stop-opacity="0"/>
        </radialGradient>
    </defs>
</svg>