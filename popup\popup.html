<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>简历自动填写助手1.0</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="popup-container">
    <!-- 头部 -->
    <div class="popup-header">
      <h1>简历助手</h1>
      <div class="header-actions">
        <button id="refresh-btn" class="icon-btn" title="刷新简历列表">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"/>
          </svg>
        </button>
        <button id="settings-btn" class="icon-btn" title="设置">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="popup-content">
      <!-- 快速操作区域 -->
      <div class="quick-actions">
        <button id="upload-btn" class="action-btn primary">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
          </svg>
          上传简历
        </button>
        <input type="file" id="file-input" accept=".doc,.docx" style="display: none;">

        <button id="create-btn" class="action-btn primary">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
          </svg>
          创建简历
        </button>

        <button id="detect-btn" class="action-btn secondary">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"/>
          </svg>
          检测表单
        </button>
      </div>

      <!-- 简历列表 -->
      <div class="resume-section">
        <div class="section-header">
          <h3>我的简历</h3>
          <span id="resume-count" class="count-badge">0</span>
        </div>

        <!-- 调试信息 -->
        <div id="debug-info" style="font-size: 12px; color: #666; margin-bottom: 10px; padding: 8px; background: #f0f0f0; border-radius: 4px;">
          <div>存储键: resumes</div>
          <div id="debug-storage-info">检查存储中...</div>
        </div>

        <div id="resume-list" class="resume-list">
          <div class="loading">加载中...</div>
        </div>
      </div>

      <!-- 状态信息 -->
      <div class="status-section">
        <div class="status-item">
          <span class="status-label">当前页面:</span>
          <span id="current-url" class="status-value">-</span>
        </div>
        <div class="status-item">
          <span class="status-label">检测到字段:</span>
          <span id="field-count" class="status-value">0</span>
        </div>
      </div>
    </div>

    <!-- 底部操作 -->
    <div class="popup-footer">
      <button id="manage-btn" class="footer-btn">
        管理简历
      </button>
      <button id="help-btn" class="footer-btn">
        帮助
      </button>
      <!-- 调试按钮（开发时使用） -->
      <button id="debug-btn" class="footer-btn">
        创建测试简历
      </button>
    </div>
  </div>

  <!-- 加载遮罩 -->
  <div id="loading-overlay" class="loading-overlay" style="display: none;">
    <div class="loading-spinner"></div>
    <p>处理中...</p>
  </div>

  <!-- 通知容器 -->
  <div id="notification-container" class="notification-container"></div>

  <!-- 脚本 -->
  <script src="../lib/utils.js"></script>
  <script src="../lib/mammoth.min.js"></script>
  <script src="../lib/document-parser.js"></script>
  <script src="../lib/resume-manager.js"></script>
  <script src="popup.js"></script>
</body>
</html>
