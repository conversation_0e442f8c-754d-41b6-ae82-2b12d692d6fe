/**
 * Popup 界面逻辑
 */

class PopupController {
  constructor() {
    this.resumeManager = new ResumeManager();
    this.documentParser = new DocumentParser();
    this.currentResumes = [];
    this.selectedResumeId = null;
    this.currentTab = null;
  }

  /**
   * 初始化
   */
  async init() {
    await this.loadCurrentTab();
    await this.loadResumes();
    this.bindEvents();
    this.updateStatus();
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    // 上传简历
    document.getElementById('upload-btn').addEventListener('click', () => {
      document.getElementById('file-input').click();
    });

    document.getElementById('file-input').addEventListener('change', (e) => {
      this.handleFileUpload(e.target.files[0]);
    });

    // 检测表单
    document.getElementById('detect-btn').addEventListener('click', () => {
      this.detectForms();
    });

    // 设置按钮
    document.getElementById('settings-btn').addEventListener('click', () => {
      chrome.runtime.openOptionsPage();
    });

    // 管理简历
    document.getElementById('manage-btn').addEventListener('click', () => {
      chrome.runtime.openOptionsPage();
    });

    // 帮助
    document.getElementById('help-btn').addEventListener('click', () => {
      this.showHelp();
    });
  }

  /**
   * 加载当前标签页信息
   */
  async loadCurrentTab() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      this.currentTab = tab;
      
      // 更新当前URL显示
      const urlElement = document.getElementById('current-url');
      if (urlElement) {
        const url = new URL(tab.url);
        urlElement.textContent = url.hostname;
        urlElement.title = tab.url;
      }
    } catch (error) {
      console.error('获取当前标签页失败:', error);
    }
  }

  /**
   * 加载简历列表
   */
  async loadResumes() {
    try {
      this.showLoading(true);
      this.currentResumes = await this.resumeManager.getResumeList();
      this.renderResumeList();
      this.updateResumeCount();
    } catch (error) {
      console.error('加载简历失败:', error);
      this.showNotification('加载简历失败', 'error');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * 渲染简历列表
   */
  renderResumeList() {
    const container = document.getElementById('resume-list');
    
    if (this.currentResumes.length === 0) {
      container.innerHTML = `
        <div class="empty-state">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
          </svg>
          <h4>还没有简历</h4>
          <p>点击"上传简历"按钮开始添加您的第一份简历</p>
        </div>
      `;
      return;
    }

    const html = this.currentResumes.map(resume => `
      <div class="resume-item" data-id="${resume.id}">
        <div class="resume-name">${resume.name}</div>
        <div class="resume-meta">
          <span>${Utils.formatDate(resume.lastModified)}</span>
          <div class="resume-completeness">
            <span>${resume.completeness}%</span>
            <div class="completeness-bar">
              <div class="completeness-fill" style="width: ${resume.completeness}%"></div>
            </div>
          </div>
        </div>
        <div class="resume-actions">
          <button class="resume-action-btn" data-action="use" data-id="${resume.id}" title="使用此简历">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
              <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"/>
            </svg>
          </button>
        </div>
      </div>
    `).join('');

    container.innerHTML = html;

    // 绑定简历项事件
    container.querySelectorAll('.resume-item').forEach(item => {
      item.addEventListener('click', (e) => {
        if (!e.target.closest('.resume-actions')) {
          this.selectResume(item.dataset.id);
        }
      });
    });

    // 绑定操作按钮事件
    container.querySelectorAll('.resume-action-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const action = btn.dataset.action;
        const resumeId = btn.dataset.id;
        
        if (action === 'use') {
          this.useResume(resumeId);
        }
      });
    });
  }

  /**
   * 选择简历
   */
  selectResume(resumeId) {
    // 移除之前的选中状态
    document.querySelectorAll('.resume-item').forEach(item => {
      item.classList.remove('selected');
    });

    // 添加新的选中状态
    const selectedItem = document.querySelector(`[data-id="${resumeId}"]`);
    if (selectedItem) {
      selectedItem.classList.add('selected');
      this.selectedResumeId = resumeId;
    }
  }

  /**
   * 使用简历进行自动填写
   */
  async useResume(resumeId) {
    try {
      if (!this.currentTab) {
        this.showNotification('无法获取当前页面信息', 'error');
        return;
      }

      this.showLoading(true, '正在自动填写...');

      // 获取简历数据
      const resume = await this.resumeManager.getResume(resumeId);
      if (!resume) {
        this.showNotification('简历数据不存在', 'error');
        return;
      }

      // 检测表单并创建映射
      const formsResponse = await chrome.tabs.sendMessage(this.currentTab.id, {
        action: 'detectForms'
      });

      if (!formsResponse || !formsResponse.success) {
        this.showNotification('无法检测到表单字段', 'error');
        return;
      }

      const allFields = formsResponse.data.flatMap(form => form.fields);

      // 发送消息到content script执行填写
      const response = await chrome.tabs.sendMessage(this.currentTab.id, {
        action: 'fillForm',
        resumeData: resume.data,
        fields: allFields
      });

      if (response && response.success) {
        this.showNotification(`成功填写 ${response.filledFields} 个字段`, 'success');
      } else {
        this.showNotification('自动填写失败', 'error');
      }

    } catch (error) {
      console.error('自动填写失败:', error);
      this.showNotification('自动填写失败', 'error');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * 处理文件上传
   */
  async handleFileUpload(file) {
    if (!file) return;

    try {
      this.showLoading(true, '正在解析文档...');

      // 解析文档
      const resumeData = await this.documentParser.parseDocument(file);
      
      // 保存简历
      const resumeId = await this.resumeManager.createResume(resumeData);
      
      // 重新加载简历列表
      await this.loadResumes();
      
      this.showNotification('简历上传成功', 'success');

    } catch (error) {
      console.error('文件上传失败:', error);
      this.showNotification(`上传失败: ${error.message}`, 'error');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * 检测表单
   */
  async detectForms() {
    try {
      if (!this.currentTab) {
        this.showNotification('无法获取当前页面信息', 'error');
        return;
      }

      this.showLoading(true, '正在检测表单...');

      // 发送消息到content script检测表单
      const response = await chrome.tabs.sendMessage(this.currentTab.id, {
        action: 'detectForms'
      });

      if (response && response.success) {
        const fieldCount = response.data.reduce((total, form) => total + form.fields.length, 0);
        this.updateFieldCount(fieldCount);
        this.showNotification(`检测到 ${fieldCount} 个可填写字段`, 'success');
      } else {
        this.showNotification('表单检测失败', 'error');
      }

    } catch (error) {
      console.error('表单检测失败:', error);
      this.showNotification('表单检测失败', 'error');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * 更新简历数量
   */
  updateResumeCount() {
    const countElement = document.getElementById('resume-count');
    if (countElement) {
      countElement.textContent = this.currentResumes.length;
    }
  }

  /**
   * 更新字段数量
   */
  updateFieldCount(count) {
    const countElement = document.getElementById('field-count');
    if (countElement) {
      countElement.textContent = count;
    }
  }

  /**
   * 更新状态信息
   */
  updateStatus() {
    // 这里可以添加更多状态更新逻辑
  }

  /**
   * 显示加载状态
   */
  showLoading(show, message = '处理中...') {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
      overlay.style.display = show ? 'flex' : 'none';
      if (show && message) {
        const textElement = overlay.querySelector('p');
        if (textElement) {
          textElement.textContent = message;
        }
      }
    }
  }

  /**
   * 显示通知
   */
  showNotification(message, type = 'info') {
    const container = document.getElementById('notification-container');
    if (!container) return;

    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;

    container.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }

  /**
   * 显示帮助
   */
  showHelp() {
    const helpUrl = chrome.runtime.getURL('help.html');
    chrome.tabs.create({ url: helpUrl });
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  const popup = new PopupController();
  popup.init();
});
