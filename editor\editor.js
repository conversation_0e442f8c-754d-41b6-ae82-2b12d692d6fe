/**
 * 简历编辑器逻辑
 */

class ResumeEditor {
  constructor() {
    this.resumeManager = new ResumeManager();
    this.currentResumeId = null;
    this.experienceCount = 1;
    this.educationCount = 1;
    this.isEditing = false;
    this.hasUnsavedChanges = false;
  }

  /**
   * 初始化编辑器
   */
  async init() {
    this.bindEvents();
    this.loadFromURL();
    this.setupAutoSave();
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    // 保存按钮
    document.getElementById('save-btn').addEventListener('click', () => {
      this.saveResume();
    });

    document.getElementById('save-resume-btn').addEventListener('click', () => {
      this.saveResume();
    });

    // 关闭按钮
    document.getElementById('close-btn').addEventListener('click', () => {
      this.closeEditor();
    });

    document.getElementById('cancel-btn').addEventListener('click', () => {
      this.closeEditor();
    });

    // 添加工作经验
    document.getElementById('add-experience').addEventListener('click', () => {
      this.addExperienceItem();
    });

    // 添加教育背景
    document.getElementById('add-education').addEventListener('click', () => {
      this.addEducationItem();
    });

    // 删除项目事件委托
    document.addEventListener('click', (e) => {
      if (e.target.closest('.remove-item')) {
        const btn = e.target.closest('.remove-item');
        const target = btn.dataset.target;
        const index = btn.dataset.index;
        this.removeItem(target, index);
      }
    });

    // 至今复选框事件
    document.addEventListener('change', (e) => {
      if (e.target.type === 'checkbox' && e.target.name.startsWith('current-')) {
        const index = e.target.name.split('-')[1];
        const endDateInput = document.getElementById(`endDate-${index}`);
        if (e.target.checked) {
          endDateInput.disabled = true;
          endDateInput.value = '';
        } else {
          endDateInput.disabled = false;
        }
      }
    });

    // 表单变化监听
    document.getElementById('resume-form').addEventListener('input', () => {
      this.hasUnsavedChanges = true;
    });

    // 页面关闭前确认
    window.addEventListener('beforeunload', (e) => {
      if (this.hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '';
      }
    });
  }

  /**
   * 从URL参数加载简历
   */
  loadFromURL() {
    const urlParams = new URLSearchParams(window.location.search);
    const resumeId = urlParams.get('id');
    
    if (resumeId) {
      this.loadResume(resumeId);
    } else {
      this.initNewResume();
    }
  }

  /**
   * 初始化新简历
   */
  initNewResume() {
    this.isEditing = false;
    this.currentResumeId = null;
    
    // 设置默认简历名称
    const now = new Date();
    const defaultName = `新简历 - ${now.toLocaleDateString('zh-CN')}`;
    document.getElementById('resumeName').value = defaultName;
  }

  /**
   * 加载现有简历
   */
  async loadResume(resumeId) {
    try {
      this.showLoading(true);
      
      const resume = await this.resumeManager.getResume(resumeId);
      if (!resume) {
        this.showNotification('简历不存在', 'error');
        this.initNewResume();
        return;
      }

      this.isEditing = true;
      this.currentResumeId = resumeId;
      this.populateForm(resume);
      
    } catch (error) {
      console.error('加载简历失败:', error);
      this.showNotification('加载简历失败', 'error');
      this.initNewResume();
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * 填充表单数据
   */
  populateForm(resume) {
    const data = resume.data;
    
    // 基本信息
    const personalInfo = data.personalInfo || {};
    document.getElementById('fullName').value = personalInfo.fullName || '';
    document.getElementById('email').value = personalInfo.email || '';
    document.getElementById('phone').value = personalInfo.phone || '';
    document.getElementById('address').value = personalInfo.address?.full || '';
    document.getElementById('city').value = personalInfo.address?.city || '';
    document.getElementById('linkedIn').value = personalInfo.linkedIn || '';
    document.getElementById('website').value = personalInfo.website || '';

    // 工作经验
    const workExperience = data.workExperience || [];
    this.clearExperienceItems();
    workExperience.forEach((exp, index) => {
      if (index > 0) this.addExperienceItem();
      this.populateExperienceItem(index, exp);
    });

    // 教育背景
    const education = data.education || [];
    this.clearEducationItems();
    education.forEach((edu, index) => {
      if (index > 0) this.addEducationItem();
      this.populateEducationItem(index, edu);
    });

    // 技能
    const skills = data.skills || [];
    document.getElementById('skills').value = skills.join(', ');

    // 简历名称
    document.getElementById('resumeName').value = resume.name || '';

    this.hasUnsavedChanges = false;
  }

  /**
   * 填充工作经验项
   */
  populateExperienceItem(index, exp) {
    document.getElementById(`company-${index}`).value = exp.company || '';
    document.getElementById(`position-${index}`).value = exp.position || '';
    document.getElementById(`startDate-${index}`).value = this.formatDateForInput(exp.startDate);
    
    if (exp.endDate) {
      document.getElementById(`endDate-${index}`).value = this.formatDateForInput(exp.endDate);
    } else {
      document.getElementById(`current-${index}`).checked = true;
      document.getElementById(`endDate-${index}`).disabled = true;
    }
    
    document.getElementById(`description-${index}`).value = exp.description || '';
  }

  /**
   * 填充教育背景项
   */
  populateEducationItem(index, edu) {
    document.getElementById(`institution-${index}`).value = edu.institution || '';
    document.getElementById(`degree-${index}`).value = edu.degree || '';
    document.getElementById(`major-${index}`).value = edu.major || '';
    document.getElementById(`graduationDate-${index}`).value = this.formatDateForInput(edu.graduationDate);
    document.getElementById(`gpa-${index}`).value = edu.gpa || '';
  }

  /**
   * 添加工作经验项
   */
  addExperienceItem() {
    const container = document.getElementById('work-experience-container');
    const index = this.experienceCount;
    
    const itemHtml = `
      <div class="experience-item" data-index="${index}">
        <div class="form-row">
          <div class="form-group">
            <label for="company-${index}">公司名称 <span class="required">*</span></label>
            <input type="text" id="company-${index}" name="company-${index}" required>
          </div>
          
          <div class="form-group">
            <label for="position-${index}">职位 <span class="required">*</span></label>
            <input type="text" id="position-${index}" name="position-${index}" required>
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label for="startDate-${index}">开始日期</label>
            <input type="date" id="startDate-${index}" name="startDate-${index}">
          </div>
          
          <div class="form-group">
            <label for="endDate-${index}">结束日期</label>
            <input type="date" id="endDate-${index}" name="endDate-${index}">
            <div class="checkbox-group">
              <input type="checkbox" id="current-${index}" name="current-${index}">
              <label for="current-${index}">至今</label>
            </div>
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-group full-width">
            <label for="description-${index}">工作描述</label>
            <textarea id="description-${index}" name="description-${index}" rows="3"></textarea>
          </div>
        </div>
        
        <div class="item-actions">
          <button type="button" class="btn btn-danger remove-item" data-target="experience" data-index="${index}">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
            </svg>
            删除
          </button>
        </div>
      </div>
    `;
    
    container.insertAdjacentHTML('beforeend', itemHtml);
    this.experienceCount++;
    this.hasUnsavedChanges = true;
  }

  /**
   * 添加教育背景项
   */
  addEducationItem() {
    const container = document.getElementById('education-container');
    const index = this.educationCount;
    
    const itemHtml = `
      <div class="education-item" data-index="${index}">
        <div class="form-row">
          <div class="form-group">
            <label for="institution-${index}">学校名称 <span class="required">*</span></label>
            <input type="text" id="institution-${index}" name="institution-${index}" required>
          </div>
          
          <div class="form-group">
            <label for="degree-${index}">学位</label>
            <input type="text" id="degree-${index}" name="degree-${index}">
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label for="major-${index}">专业 <span class="required">*</span></label>
            <input type="text" id="major-${index}" name="major-${index}" required>
          </div>
          
          <div class="form-group">
            <label for="graduationDate-${index}">毕业日期</label>
            <input type="date" id="graduationDate-${index}" name="graduationDate-${index}">
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label for="gpa-${index}">GPA</label>
            <input type="number" id="gpa-${index}" name="gpa-${index}" step="0.01" min="0" max="4">
          </div>
        </div>
        
        <div class="item-actions">
          <button type="button" class="btn btn-danger remove-item" data-target="education" data-index="${index}">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
            </svg>
            删除
          </button>
        </div>
      </div>
    `;
    
    container.insertAdjacentHTML('beforeend', itemHtml);
    this.educationCount++;
    this.hasUnsavedChanges = true;
  }

  /**
   * 删除项目
   */
  removeItem(target, index) {
    const item = document.querySelector(`[data-index="${index}"]`);
    if (item) {
      item.remove();
      this.hasUnsavedChanges = true;
    }
  }

  /**
   * 清空工作经验项
   */
  clearExperienceItems() {
    const container = document.getElementById('work-experience-container');
    container.innerHTML = '';
    this.experienceCount = 0;
    this.addExperienceItem();
  }

  /**
   * 清空教育背景项
   */
  clearEducationItems() {
    const container = document.getElementById('education-container');
    container.innerHTML = '';
    this.educationCount = 0;
    this.addEducationItem();
  }

  /**
   * 收集表单数据
   */
  collectFormData() {
    const formData = new FormData(document.getElementById('resume-form'));
    
    // 基本信息
    const personalInfo = {
      fullName: formData.get('fullName'),
      email: formData.get('email'),
      phone: formData.get('phone'),
      address: {
        full: formData.get('address'),
        city: formData.get('city'),
        country: '中国'
      },
      linkedIn: formData.get('linkedIn'),
      website: formData.get('website')
    };

    // 工作经验
    const workExperience = [];
    const experienceItems = document.querySelectorAll('.experience-item');
    experienceItems.forEach(item => {
      const index = item.dataset.index;
      const company = formData.get(`company-${index}`);
      const position = formData.get(`position-${index}`);
      
      if (company && position) {
        const isCurrent = formData.get(`current-${index}`) === 'on';
        workExperience.push({
          company,
          position,
          startDate: formData.get(`startDate-${index}`) || null,
          endDate: isCurrent ? null : formData.get(`endDate-${index}`) || null,
          description: formData.get(`description-${index}`) || ''
        });
      }
    });

    // 教育背景
    const education = [];
    const educationItems = document.querySelectorAll('.education-item');
    educationItems.forEach(item => {
      const index = item.dataset.index;
      const institution = formData.get(`institution-${index}`);
      const major = formData.get(`major-${index}`);
      
      if (institution && major) {
        education.push({
          institution,
          degree: formData.get(`degree-${index}`) || '',
          major,
          graduationDate: formData.get(`graduationDate-${index}`) || null,
          gpa: parseFloat(formData.get(`gpa-${index}`)) || null
        });
      }
    });

    // 技能
    const skillsText = formData.get('skills') || '';
    const skills = skillsText.split(',').map(skill => skill.trim()).filter(skill => skill);

    return {
      personalInfo,
      workExperience,
      education,
      skills,
      metadata: {
        createdBy: 'manual',
        createdDate: new Date().toISOString()
      }
    };
  }

  /**
   * 验证表单
   */
  validateForm() {
    const errors = [];
    
    // 验证必填字段
    const requiredFields = [
      { id: 'fullName', name: '姓名' },
      { id: 'email', name: '邮箱' },
      { id: 'phone', name: '电话' },
      { id: 'resumeName', name: '简历名称' }
    ];

    requiredFields.forEach(field => {
      const element = document.getElementById(field.id);
      const value = element.value.trim();
      
      if (!value) {
        errors.push(`${field.name}不能为空`);
        this.markFieldError(element);
      } else {
        this.clearFieldError(element);
      }
    });

    // 验证邮箱格式
    const email = document.getElementById('email').value.trim();
    if (email && !Utils.isValidEmail(email)) {
      errors.push('邮箱格式不正确');
      this.markFieldError(document.getElementById('email'));
    }

    // 验证电话格式
    const phone = document.getElementById('phone').value.trim();
    if (phone && !Utils.isValidPhone(phone)) {
      errors.push('电话格式不正确');
      this.markFieldError(document.getElementById('phone'));
    }

    return errors;
  }

  /**
   * 标记字段错误
   */
  markFieldError(element) {
    element.closest('.form-group').classList.add('error');
  }

  /**
   * 清除字段错误
   */
  clearFieldError(element) {
    element.closest('.form-group').classList.remove('error');
  }

  /**
   * 保存简历
   */
  async saveResume() {
    try {
      this.showLoading(true);
      
      // 验证表单
      const errors = this.validateForm();
      if (errors.length > 0) {
        this.showNotification(`请修正以下错误：\n${errors.join('\n')}`, 'error');
        return;
      }

      // 收集数据
      const resumeData = this.collectFormData();
      const resumeName = document.getElementById('resumeName').value.trim();

      let resumeId;
      if (this.isEditing && this.currentResumeId) {
        // 更新现有简历
        await this.resumeManager.updateResume(this.currentResumeId, {
          name: resumeName,
          data: resumeData
        });
        resumeId = this.currentResumeId;
      } else {
        // 创建新简历
        resumeId = await this.resumeManager.createResume(resumeData, resumeName);
        this.currentResumeId = resumeId;
        this.isEditing = true;
      }

      this.hasUnsavedChanges = false;
      this.showNotification('简历保存成功', 'success');

      // 更新URL
      const newUrl = `${window.location.pathname}?id=${resumeId}`;
      window.history.replaceState({}, '', newUrl);

      // 确保存储事件有时间传播
      await new Promise(resolve => setTimeout(resolve, 200));

      // 验证保存结果（调试用）
      console.log('简历保存完成，ID:', resumeId);
      const savedResume = await this.resumeManager.getResume(resumeId);
      console.log('验证保存的简历:', savedResume);

    } catch (error) {
      console.error('保存简历失败:', error);
      this.showNotification('保存失败，请重试', 'error');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * 关闭编辑器
   */
  closeEditor() {
    if (this.hasUnsavedChanges) {
      if (!confirm('您有未保存的更改，确定要关闭吗？')) {
        return;
      }
    }
    
    // 返回到选项页面
    window.location.href = '../options/options.html';
  }

  /**
   * 设置自动保存
   */
  setupAutoSave() {
    // 每30秒自动保存一次（如果有更改）
    setInterval(() => {
      if (this.hasUnsavedChanges && this.isEditing) {
        this.saveResume();
      }
    }, 30000);
  }

  /**
   * 格式化日期为输入框格式
   */
  formatDateForInput(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';
    return date.toISOString().split('T')[0];
  }

  /**
   * 显示加载状态
   */
  showLoading(show) {
    const container = document.querySelector('.editor-container');
    if (show) {
      container.classList.add('loading');
    } else {
      container.classList.remove('loading');
    }
  }

  /**
   * 显示通知
   */
  showNotification(message, type = 'info') {
    const container = document.getElementById('notification-container');
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    container.appendChild(notification);
    
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  const editor = new ResumeEditor();
  editor.init();
});
