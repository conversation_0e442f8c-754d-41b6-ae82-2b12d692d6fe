/**
 * Options 页面逻辑
 */

class OptionsController {
  constructor() {
    this.resumeManager = new ResumeManager();
    this.documentParser = new DocumentParser();
    this.currentResumes = [];
    this.currentSettings = {};
    this.activeTab = 'resumes';
  }

  /**
   * 初始化
   */
  async init() {
    await this.loadSettings();
    await this.loadResumes();
    this.bindEvents();
    this.initTabs();
    this.updateUI();
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    // 标签页切换
    document.querySelectorAll('.nav-item').forEach(item => {
      item.addEventListener('click', (e) => {
        e.preventDefault();
        const tab = item.dataset.tab;
        this.switchTab(tab);
      });
    });

    // 创建简历
    document.getElementById('create-resume-btn').addEventListener('click', () => {
      this.createNewResume();
    });

    // 上传简历
    document.getElementById('upload-resume-btn').addEventListener('click', () => {
      document.getElementById('resume-file-input').click();
    });

    document.getElementById('resume-file-input').addEventListener('change', (e) => {
      this.handleResumeUpload(e.target.files[0]);
    });

    // 设置相关事件
    this.bindSettingsEvents();

    // AI设置事件
    this.bindAIEvents();

    // 数据管理事件
    this.bindDataEvents();
  }

  /**
   * 绑定设置事件
   */
  bindSettingsEvents() {
    // 自动填写开关
    document.getElementById('auto-fill-enabled').addEventListener('change', (e) => {
      this.updateSetting('autoFillEnabled', e.target.checked);
    });

    // 高亮字段开关
    document.getElementById('highlight-fields').addEventListener('change', (e) => {
      this.updateSetting('highlightFilledFields', e.target.checked);
    });

    // 默认简历选择
    document.getElementById('default-resume').addEventListener('change', (e) => {
      this.updateSetting('defaultResumeId', e.target.value);
    });
  }

  /**
   * 绑定AI事件
   */
  bindAIEvents() {
    // AI功能开关
    document.getElementById('ai-enabled').addEventListener('change', (e) => {
      this.toggleAIConfig(e.target.checked);
      this.updateSetting('aiEnabled', e.target.checked);
    });

    // API密钥
    document.getElementById('ai-api-key').addEventListener('change', (e) => {
      this.updateAISetting('apiKey', e.target.value);
    });

    // API端点
    document.getElementById('ai-endpoint').addEventListener('change', (e) => {
      this.updateAISetting('endpoint', e.target.value);
    });

    // 测试AI连接
    document.getElementById('test-ai-btn').addEventListener('click', () => {
      this.testAIConnection();
    });
  }

  /**
   * 绑定数据管理事件
   */
  bindDataEvents() {
    // 导出数据
    document.getElementById('export-data-btn').addEventListener('click', () => {
      this.exportData();
    });

    // 导入数据
    document.getElementById('import-data-btn').addEventListener('click', () => {
      document.getElementById('import-file-input').click();
    });

    document.getElementById('import-file-input').addEventListener('change', (e) => {
      this.importData(e.target.files[0]);
    });

    // 清除数据
    document.getElementById('clear-data-btn').addEventListener('click', () => {
      this.confirmClearData();
    });

    // 确认对话框
    document.getElementById('confirm-cancel').addEventListener('click', () => {
      this.hideConfirmDialog();
    });

    document.getElementById('confirm-ok').addEventListener('click', () => {
      this.executeConfirmedAction();
    });
  }

  /**
   * 初始化标签页
   */
  initTabs() {
    // 从URL hash获取初始标签页
    const hash = window.location.hash.substring(1);
    if (hash && document.getElementById(`${hash}-tab`)) {
      this.switchTab(hash);
    }
  }

  /**
   * 切换标签页
   */
  switchTab(tabName) {
    // 更新导航状态
    document.querySelectorAll('.nav-item').forEach(item => {
      item.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // 更新内容显示
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.remove('active');
    });
    document.getElementById(`${tabName}-tab`).classList.add('active');

    this.activeTab = tabName;
    window.location.hash = tabName;

    // 根据标签页加载相应数据
    if (tabName === 'resumes') {
      this.loadResumes();
    }
  }

  /**
   * 加载设置
   */
  async loadSettings() {
    try {
      this.currentSettings = await this.resumeManager.getSettings();
    } catch (error) {
      console.error('加载设置失败:', error);
      this.showNotification('加载设置失败', 'error');
    }
  }

  /**
   * 加载简历列表
   */
  async loadResumes() {
    try {
      this.currentResumes = await this.resumeManager.getResumeList();
      this.renderResumeGrid();
      this.updateDefaultResumeSelect();
    } catch (error) {
      console.error('加载简历失败:', error);
      this.showNotification('加载简历失败', 'error');
    }
  }

  /**
   * 渲染简历网格
   */
  renderResumeGrid() {
    const grid = document.getElementById('resume-grid');
    
    if (this.currentResumes.length === 0) {
      grid.innerHTML = `
        <div class="empty-state">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
          </svg>
          <h3>还没有简历</h3>
          <p>点击"上传简历"按钮开始添加您的简历档案</p>
        </div>
      `;
      return;
    }

    const html = this.currentResumes.map(resume => `
      <div class="resume-card" data-id="${resume.id}">
        <div class="resume-card-header">
          <div>
            <div class="resume-title">${resume.name}</div>
            <div class="resume-meta">
              创建于 ${Utils.formatDate(resume.createdDate)} | 
              更新于 ${Utils.formatDate(resume.lastModified)}
            </div>
          </div>
          <div class="resume-actions">
            <button class="action-btn" data-action="edit" data-id="${resume.id}" title="编辑">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"/>
              </svg>
            </button>
            <button class="action-btn" data-action="duplicate" data-id="${resume.id}" title="复制">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/>
              </svg>
            </button>
            <button class="action-btn" data-action="export" data-id="${resume.id}" title="导出">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
              </svg>
            </button>
            <button class="action-btn" data-action="delete" data-id="${resume.id}" title="删除">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
              </svg>
            </button>
          </div>
        </div>
        
        <div class="resume-stats">
          <div class="completeness-info">
            <span>完整度: ${resume.completeness}%</span>
            <div class="completeness-bar">
              <div class="completeness-fill" style="width: ${resume.completeness}%"></div>
            </div>
          </div>
        </div>
        
        <div class="resume-preview">
          ${this.generateResumePreview(resume)}
        </div>
      </div>
    `).join('');

    grid.innerHTML = html;

    // 绑定操作按钮事件
    grid.querySelectorAll('.action-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const action = btn.dataset.action;
        const resumeId = btn.dataset.id;
        this.handleResumeAction(action, resumeId);
      });
    });
  }

  /**
   * 生成简历预览
   */
  generateResumePreview(resume) {
    const data = resume.data || {};
    const personalInfo = data.personalInfo || {};
    
    let preview = '';
    if (personalInfo.fullName) {
      preview += `姓名: ${personalInfo.fullName}<br>`;
    }
    if (personalInfo.email) {
      preview += `邮箱: ${personalInfo.email}<br>`;
    }
    if (data.workExperience && data.workExperience.length > 0) {
      preview += `工作经验: ${data.workExperience.length} 项<br>`;
    }
    if (data.education && data.education.length > 0) {
      preview += `教育背景: ${data.education.length} 项`;
    }
    
    return preview || '暂无预览信息';
  }

  /**
   * 处理简历操作
   */
  async handleResumeAction(action, resumeId) {
    try {
      switch (action) {
        case 'edit':
          this.editResume(resumeId);
          break;
        case 'duplicate':
          await this.duplicateResume(resumeId);
          break;
        case 'export':
          await this.exportResume(resumeId);
          break;
        case 'delete':
          this.confirmDeleteResume(resumeId);
          break;
      }
    } catch (error) {
      console.error(`执行操作 ${action} 失败:`, error);
      this.showNotification('操作失败', 'error');
    }
  }

  /**
   * 创建新简历
   */
  createNewResume() {
    const editorUrl = chrome.runtime.getURL('editor/editor.html');
    chrome.tabs.create({ url: editorUrl });
  }

  /**
   * 编辑简历
   */
  editResume(resumeId) {
    const editorUrl = chrome.runtime.getURL(`editor/editor.html?id=${resumeId}`);
    chrome.tabs.create({ url: editorUrl });
  }

  /**
   * 复制简历
   */
  async duplicateResume(resumeId) {
    try {
      await this.resumeManager.duplicateResume(resumeId);
      await this.loadResumes();
      this.showNotification('简历复制成功', 'success');
    } catch (error) {
      console.error('复制简历失败:', error);
      this.showNotification('复制失败', 'error');
    }
  }

  /**
   * 导出单个简历
   */
  async exportResume(resumeId) {
    try {
      const jsonData = await this.resumeManager.exportResume(resumeId);
      const resume = this.currentResumes.find(r => r.id === resumeId);
      const filename = `${resume.name}.json`;
      
      this.downloadFile(jsonData, filename, 'application/json');
      this.showNotification('简历导出成功', 'success');
    } catch (error) {
      console.error('导出简历失败:', error);
      this.showNotification('导出失败', 'error');
    }
  }

  /**
   * 确认删除简历
   */
  confirmDeleteResume(resumeId) {
    const resume = this.currentResumes.find(r => r.id === resumeId);
    this.showConfirmDialog(
      '删除简历',
      `确定要删除简历"${resume.name}"吗？此操作不可恢复。`,
      () => this.deleteResume(resumeId)
    );
  }

  /**
   * 删除简历
   */
  async deleteResume(resumeId) {
    try {
      await this.resumeManager.deleteResume(resumeId);
      await this.loadResumes();
      this.showNotification('简历删除成功', 'success');
    } catch (error) {
      console.error('删除简历失败:', error);
      this.showNotification('删除失败', 'error');
    }
  }

  /**
   * 处理简历上传
   */
  async handleResumeUpload(file) {
    if (!file) return;

    try {
      this.showLoading(true);
      
      const resumeData = await this.documentParser.parseDocument(file);
      await this.resumeManager.createResume(resumeData);
      await this.loadResumes();
      
      this.showNotification('简历上传成功', 'success');
    } catch (error) {
      console.error('上传简历失败:', error);
      this.showNotification(`上传失败: ${error.message}`, 'error');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * 更新设置
   */
  async updateSetting(key, value) {
    try {
      this.currentSettings[key] = value;
      await this.resumeManager.saveSettings(this.currentSettings);
    } catch (error) {
      console.error('保存设置失败:', error);
      this.showNotification('保存设置失败', 'error');
    }
  }

  /**
   * 更新AI设置
   */
  async updateAISetting(key, value) {
    try {
      if (!this.currentSettings.aiConfig) {
        this.currentSettings.aiConfig = {};
      }
      this.currentSettings.aiConfig[key] = value;
      await this.resumeManager.saveSettings(this.currentSettings);
    } catch (error) {
      console.error('保存AI设置失败:', error);
      this.showNotification('保存AI设置失败', 'error');
    }
  }

  /**
   * 切换AI配置显示
   */
  toggleAIConfig(enabled) {
    const configDiv = document.getElementById('ai-config');
    configDiv.style.display = enabled ? 'block' : 'none';
  }

  /**
   * 测试AI连接
   */
  async testAIConnection() {
    this.showNotification('AI连接测试功能开发中', 'info');
  }

  /**
   * 更新默认简历选择框
   */
  updateDefaultResumeSelect() {
    const select = document.getElementById('default-resume');
    select.innerHTML = '<option value="">请选择默认简历...</option>';
    
    this.currentResumes.forEach(resume => {
      const option = document.createElement('option');
      option.value = resume.id;
      option.textContent = resume.name;
      if (resume.id === this.currentSettings.defaultResumeId) {
        option.selected = true;
      }
      select.appendChild(option);
    });
  }

  /**
   * 更新UI状态
   */
  updateUI() {
    // 更新设置UI
    document.getElementById('auto-fill-enabled').checked = this.currentSettings.autoFillEnabled !== false;
    document.getElementById('highlight-fields').checked = this.currentSettings.highlightFilledFields !== false;
    document.getElementById('ai-enabled').checked = this.currentSettings.aiEnabled === true;
    
    // 更新AI配置显示
    this.toggleAIConfig(this.currentSettings.aiEnabled === true);
    
    // 更新AI配置值
    if (this.currentSettings.aiConfig) {
      document.getElementById('ai-api-key').value = this.currentSettings.aiConfig.apiKey || '';
      document.getElementById('ai-endpoint').value = this.currentSettings.aiConfig.endpoint || '';
    }
  }

  /**
   * 导出所有数据
   */
  async exportData() {
    try {
      const resumes = await this.resumeManager.getAllResumes();
      const settings = await this.resumeManager.getSettings();
      
      const exportData = {
        resumes,
        settings,
        exportDate: new Date().toISOString(),
        version: '1.0'
      };
      
      const jsonData = JSON.stringify(exportData, null, 2);
      const filename = `resume-autofill-backup-${new Date().toISOString().split('T')[0]}.json`;
      
      this.downloadFile(jsonData, filename, 'application/json');
      this.showNotification('数据导出成功', 'success');
    } catch (error) {
      console.error('导出数据失败:', error);
      this.showNotification('导出失败', 'error');
    }
  }

  /**
   * 导入数据
   */
  async importData(file) {
    if (!file) return;

    try {
      const text = await this.readFileAsText(file);
      const importData = JSON.parse(text);
      
      if (importData.resumes) {
        await chrome.storage.local.set({ resumes: importData.resumes });
      }
      
      if (importData.settings) {
        await chrome.storage.local.set({ settings: importData.settings });
      }
      
      await this.loadSettings();
      await this.loadResumes();
      this.updateUI();
      
      this.showNotification('数据导入成功', 'success');
    } catch (error) {
      console.error('导入数据失败:', error);
      this.showNotification('导入失败，请检查文件格式', 'error');
    }
  }

  /**
   * 确认清除数据
   */
  confirmClearData() {
    this.showConfirmDialog(
      '清除所有数据',
      '确定要清除所有简历和设置数据吗？此操作不可恢复。',
      () => this.clearAllData()
    );
  }

  /**
   * 清除所有数据
   */
  async clearAllData() {
    try {
      await this.resumeManager.clearAllData();
      await this.loadSettings();
      await this.loadResumes();
      this.updateUI();
      this.showNotification('数据清除成功', 'success');
    } catch (error) {
      console.error('清除数据失败:', error);
      this.showNotification('清除失败', 'error');
    }
  }

  /**
   * 显示确认对话框
   */
  showConfirmDialog(title, message, callback) {
    document.getElementById('confirm-title').textContent = title;
    document.getElementById('confirm-message').textContent = message;
    document.getElementById('confirm-dialog').style.display = 'flex';
    
    this.confirmCallback = callback;
  }

  /**
   * 隐藏确认对话框
   */
  hideConfirmDialog() {
    document.getElementById('confirm-dialog').style.display = 'none';
    this.confirmCallback = null;
  }

  /**
   * 执行确认的操作
   */
  executeConfirmedAction() {
    if (this.confirmCallback) {
      this.confirmCallback();
    }
    this.hideConfirmDialog();
  }

  /**
   * 显示通知
   */
  showNotification(message, type = 'info') {
    const container = document.getElementById('notification-container');
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    container.appendChild(notification);
    
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }

  /**
   * 显示加载状态
   */
  showLoading(show) {
    // 这里可以添加加载状态显示逻辑
  }

  /**
   * 下载文件
   */
  downloadFile(content, filename, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  /**
   * 读取文件为文本
   */
  readFileAsText(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = () => reject(reader.error);
      reader.readAsText(file);
    });
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  const options = new OptionsController();
  options.init();
});
