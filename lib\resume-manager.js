/**
 * 简历管理器 - 管理多个简历档案的CRUD操作
 */

class ResumeManager {
  constructor() {
    this.storageKey = 'resumes';
    this.settingsKey = 'settings';
  }

  /**
   * 创建新简历
   */
  async createResume(resumeData, name = null) {
    try {
      const resumeId = Utils.generateId();
      const now = new Date().toISOString();
      
      const resumeProfile = {
        id: resumeId,
        name: name || this.generateResumeName(resumeData),
        data: resumeData,
        createdDate: now,
        lastModified: now,
        completeness: Utils.calculateCompleteness(resumeData)
      };

      // 获取现有简历
      const resumes = await this.getAllResumes();
      resumes[resumeId] = resumeProfile;

      // 保存到存储
      await this.saveToStorage(resumes);

      return resumeId;
    } catch (error) {
      console.error('创建简历失败:', error);
      throw error;
    }
  }

  /**
   * 更新简历
   */
  async updateResume(resumeId, updates) {
    try {
      const resumes = await this.getAllResumes();
      
      if (!resumes[resumeId]) {
        throw new Error('简历不存在');
      }

      // 更新简历数据
      const resume = resumes[resumeId];
      
      if (updates.name !== undefined) {
        resume.name = updates.name;
      }
      
      if (updates.data !== undefined) {
        resume.data = { ...resume.data, ...updates.data };
        resume.completeness = Utils.calculateCompleteness(resume.data);
      }
      
      resume.lastModified = new Date().toISOString();

      // 保存更新
      await this.saveToStorage(resumes);

      return resume;
    } catch (error) {
      console.error('更新简历失败:', error);
      throw error;
    }
  }

  /**
   * 删除简历
   */
  async deleteResume(resumeId) {
    try {
      const resumes = await this.getAllResumes();
      
      if (!resumes[resumeId]) {
        throw new Error('简历不存在');
      }

      delete resumes[resumeId];
      await this.saveToStorage(resumes);

      // 如果删除的是默认简历，清除默认设置
      const settings = await this.getSettings();
      if (settings.defaultResumeId === resumeId) {
        settings.defaultResumeId = null;
        await this.saveSettings(settings);
      }

      return true;
    } catch (error) {
      console.error('删除简历失败:', error);
      throw error;
    }
  }

  /**
   * 获取单个简历
   */
  async getResume(resumeId) {
    try {
      const resumes = await this.getAllResumes();
      return resumes[resumeId] || null;
    } catch (error) {
      console.error('获取简历失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有简历
   */
  async getAllResumes() {
    try {
      const result = await chrome.storage.local.get([this.storageKey]);
      return result[this.storageKey] || {};
    } catch (error) {
      console.error('获取简历列表失败:', error);
      return {};
    }
  }

  /**
   * 获取简历列表（用于显示）
   */
  async getResumeList() {
    try {
      const resumes = await this.getAllResumes();
      return Object.values(resumes).map(resume => ({
        id: resume.id,
        name: resume.name,
        completeness: resume.completeness,
        lastModified: resume.lastModified,
        createdDate: resume.createdDate
      })).sort((a, b) => new Date(b.lastModified) - new Date(a.lastModified));
    } catch (error) {
      console.error('获取简历列表失败:', error);
      return [];
    }
  }

  /**
   * 验证简历数据
   */
  validateResume(resumeData) {
    const errors = [];
    const warnings = [];

    if (!resumeData) {
      errors.push('简历数据不能为空');
      return { isValid: false, errors, warnings };
    }

    // 验证个人信息
    const personalInfo = resumeData.personalInfo || {};
    
    if (!personalInfo.fullName || !personalInfo.fullName.trim()) {
      warnings.push('缺少姓名信息');
    }

    if (!personalInfo.email || !Utils.isValidEmail(personalInfo.email)) {
      warnings.push('缺少有效的邮箱地址');
    }

    if (!personalInfo.phone || !Utils.isValidPhone(personalInfo.phone)) {
      warnings.push('缺少有效的电话号码');
    }

    // 验证工作经验
    if (!resumeData.workExperience || resumeData.workExperience.length === 0) {
      warnings.push('缺少工作经验信息');
    } else {
      resumeData.workExperience.forEach((job, index) => {
        if (!job.company || !job.position) {
          warnings.push(`工作经验 ${index + 1} 缺少公司或职位信息`);
        }
      });
    }

    // 验证教育背景
    if (!resumeData.education || resumeData.education.length === 0) {
      warnings.push('缺少教育背景信息');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      completeness: Utils.calculateCompleteness(resumeData)
    };
  }

  /**
   * 复制简历
   */
  async duplicateResume(resumeId, newName = null) {
    try {
      const originalResume = await this.getResume(resumeId);
      if (!originalResume) {
        throw new Error('原简历不存在');
      }

      const duplicatedData = Utils.deepClone(originalResume.data);
      const name = newName || `${originalResume.name} - 副本`;

      return await this.createResume(duplicatedData, name);
    } catch (error) {
      console.error('复制简历失败:', error);
      throw error;
    }
  }

  /**
   * 导出简历数据
   */
  async exportResume(resumeId) {
    try {
      const resume = await this.getResume(resumeId);
      if (!resume) {
        throw new Error('简历不存在');
      }

      const exportData = {
        name: resume.name,
        data: resume.data,
        exportDate: new Date().toISOString(),
        version: '1.0'
      };

      return JSON.stringify(exportData, null, 2);
    } catch (error) {
      console.error('导出简历失败:', error);
      throw error;
    }
  }

  /**
   * 导入简历数据
   */
  async importResume(jsonData, name = null) {
    try {
      const importData = JSON.parse(jsonData);
      
      if (!importData.data) {
        throw new Error('无效的简历数据格式');
      }

      const resumeName = name || importData.name || '导入的简历';
      return await this.createResume(importData.data, resumeName);
    } catch (error) {
      console.error('导入简历失败:', error);
      throw error;
    }
  }

  /**
   * 获取设置
   */
  async getSettings() {
    try {
      const result = await chrome.storage.local.get([this.settingsKey]);
      return result[this.settingsKey] || {
        defaultResumeId: null,
        autoFillEnabled: true,
        aiEnabled: false,
        highlightFilledFields: true
      };
    } catch (error) {
      console.error('获取设置失败:', error);
      return {};
    }
  }

  /**
   * 保存设置
   */
  async saveSettings(settings) {
    try {
      await chrome.storage.local.set({ [this.settingsKey]: settings });
      return true;
    } catch (error) {
      console.error('保存设置失败:', error);
      throw error;
    }
  }

  /**
   * 设置默认简历
   */
  async setDefaultResume(resumeId) {
    try {
      const settings = await this.getSettings();
      settings.defaultResumeId = resumeId;
      await this.saveSettings(settings);
      return true;
    } catch (error) {
      console.error('设置默认简历失败:', error);
      throw error;
    }
  }

  /**
   * 获取默认简历
   */
  async getDefaultResume() {
    try {
      const settings = await this.getSettings();
      if (settings.defaultResumeId) {
        return await this.getResume(settings.defaultResumeId);
      }
      return null;
    } catch (error) {
      console.error('获取默认简历失败:', error);
      return null;
    }
  }

  /**
   * 保存到存储
   */
  async saveToStorage(resumes) {
    await chrome.storage.local.set({ [this.storageKey]: resumes });
  }

  /**
   * 生成简历名称
   */
  generateResumeName(resumeData) {
    const personalInfo = resumeData.personalInfo || {};
    const name = personalInfo.fullName || '未命名';
    const date = new Date().toLocaleDateString('zh-CN');
    return `${name}的简历 - ${date}`;
  }

  /**
   * 清理存储
   */
  async clearAllData() {
    try {
      await chrome.storage.local.remove([this.storageKey, this.settingsKey]);
      return true;
    } catch (error) {
      console.error('清理数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取存储使用情况
   */
  async getStorageUsage() {
    try {
      const result = await chrome.storage.local.getBytesInUse();
      return {
        used: result,
        quota: chrome.storage.local.QUOTA_BYTES,
        percentage: (result / chrome.storage.local.QUOTA_BYTES) * 100
      };
    } catch (error) {
      console.error('获取存储使用情况失败:', error);
      return { used: 0, quota: 0, percentage: 0 };
    }
  }
}

// 导出到全局
if (typeof window !== 'undefined') {
  window.ResumeManager = ResumeManager;
}
