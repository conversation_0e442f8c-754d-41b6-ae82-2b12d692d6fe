# 简历自动填写助手

一个智能的Microsoft Edge浏览器扩展，帮助求职者快速、准确地填写求职申请表单。

## 功能特性

### 🚀 核心功能
- **DOC文件解析**：支持上传DOC/DOCX格式简历，自动提取个人信息
- **手动创建简历**：提供可视化编辑器，支持手动创建和编辑简历
- **多简历管理**：保存和管理多个简历档案，适用于不同职位申请
- **智能表单检测**：自动识别网页表单字段，判断字段用途
- **一键自动填写**：智能匹配简历信息到表单字段
- **数据本地存储**：所有数据仅存储在本地，保护隐私安全

### 🤖 AI增强功能
- **智能字段识别**：AI分析页面内容，提供更准确的字段识别
- **内容智能匹配**：AI优化简历内容与表单字段的匹配
- **降级机制**：AI服务不可用时自动切换到基本模式

### 🛡️ 安全特性
- **本地数据存储**：简历数据仅存储在用户本地
- **隐私保护**：不收集或上传用户个人信息
- **数据加密**：敏感信息本地加密存储
- **权限最小化**：仅请求必要的浏览器权限

## 项目结构

```
resume-autofill-extension/
├── manifest.json              # 扩展配置文件
├── background/
│   └── background.js          # 后台脚本
├── content/
│   ├── content.js            # 内容脚本
│   └── content.css           # 内容脚本样式
├── popup/
│   ├── popup.html            # 弹窗界面
│   ├── popup.css             # 弹窗样式
│   └── popup.js              # 弹窗逻辑
├── options/
│   ├── options.html          # 选项页面
│   ├── options.css           # 选项页面样式
│   └── options.js            # 选项页面逻辑
├── editor/
│   ├── editor.html           # 简历编辑器页面
│   ├── editor.css            # 编辑器样式
│   └── editor.js             # 编辑器逻辑
├── lib/
│   ├── mammoth.min.js        # DOC文件解析库
│   ├── utils.js              # 工具函数
│   ├── document-parser.js    # 文档解析器
│   ├── resume-manager.js     # 简历管理器
│   ├── ai-service.js         # AI服务
│   └── error-handler.js      # 错误处理器
├── icons/
│   └── icon.svg              # 扩展图标
├── help.html                 # 帮助文档
└── README.md                 # 项目说明
```

## 技术架构

### 架构设计
- **Manifest V3**：使用最新的扩展规范
- **模块化设计**：各功能模块独立，便于维护
- **事件驱动**：基于消息传递的组件通信
- **错误处理**：完善的错误处理和恢复机制

### 核心组件
1. **DocumentParser**：解析DOC文件，提取简历信息
2. **ResumeManager**：管理简历档案的CRUD操作
3. **FormDetector**：检测和分析网页表单字段
4. **AutoFillEngine**：执行自动填写操作
5. **AIService**：提供AI增强功能
6. **ErrorHandler**：统一错误处理

## 安装和使用

### 安装方法
1. 下载扩展文件到本地
2. 打开Edge浏览器，进入扩展管理页面
3. 启用"开发者模式"
4. 点击"加载解压缩的扩展"
5. 选择扩展文件夹

### 使用步骤
1. **添加简历**：
   - 上传DOC格式简历文件自动解析，或
   - 点击"创建简历"手动填写信息
2. **检测表单**：在求职网站上点击"检测表单"按钮
3. **自动填写**：选择简历档案，点击"自动填写"
4. **检查调整**：检查填写结果，手动调整不准确的内容

## 开发说明

### 技术栈
- **JavaScript ES6+**：纯JavaScript开发，无TypeScript
- **Chrome Extension APIs**：使用标准的浏览器扩展API
- **Mammoth.js**：用于解析DOC文件
- **CSS3**：现代CSS样式

### 开发环境
- Microsoft Edge 浏览器（最新版本）
- 文本编辑器或IDE
- 浏览器开发者工具

### 调试方法
1. 在扩展管理页面点击"检查视图"
2. 使用浏览器开发者工具调试
3. 查看控制台错误信息
4. 使用断点调试JavaScript代码

## 配置说明

### 基本设置
- **自动填写开关**：启用/禁用自动填写功能
- **字段高亮**：填写后高亮显示已填写字段
- **默认简历**：设置默认使用的简历档案

### AI设置
- **AI功能开关**：启用/禁用AI增强功能
- **API密钥**：配置AI服务的API密钥
- **API端点**：配置AI服务的端点地址

## 数据格式

### 简历数据结构
```javascript
{
  personalInfo: {
    fullName: "张三",
    email: "<EMAIL>",
    phone: "13800138000",
    address: {
      full: "北京市朝阳区",
      city: "北京",
      country: "中国"
    }
  },
  workExperience: [{
    company: "ABC公司",
    position: "软件工程师",
    startDate: "2020-01-01",
    endDate: "2023-12-31",
    description: "负责前端开发工作"
  }],
  education: [{
    institution: "北京大学",
    degree: "学士",
    major: "计算机科学与技术",
    graduationDate: "2020-06-30"
  }],
  skills: ["JavaScript", "React", "Node.js"]
}
```

## 错误处理

### 错误类型
- **网络错误**：AI服务连接失败
- **文件错误**：DOC文件解析失败
- **存储错误**：本地存储空间不足
- **权限错误**：浏览器权限不足

### 恢复机制
- **自动重试**：网络请求失败时自动重试
- **降级处理**：AI服务不可用时切换到基本模式
- **错误日志**：记录错误信息便于调试

## 隐私保护

### 数据安全
- 所有简历数据仅存储在用户本地浏览器中
- 不会向任何第三方服务器上传个人信息
- AI功能使用时对数据进行脱敏处理

### 权限说明
- `storage`：用于本地数据存储
- `activeTab`：用于访问当前标签页
- `scripting`：用于注入内容脚本

## 版本历史

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持DOC文件解析
- 实现基本的表单检测和自动填写
- 添加多简历管理功能
- 集成AI增强功能
- 完善错误处理机制

## 贡献指南

欢迎提交问题报告和功能建议！

### 开发流程
1. Fork项目仓库
2. 创建功能分支
3. 提交代码更改
4. 创建Pull Request

### 代码规范
- 使用ES6+语法
- 遵循JavaScript标准代码风格
- 添加适当的注释
- 编写错误处理代码

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues页面
- 技术支持邮箱
- 用户反馈表单

---

**简历自动填写助手** - 让求职申请更简单、更高效！
