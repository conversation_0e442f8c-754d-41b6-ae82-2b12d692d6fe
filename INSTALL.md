# 安装指南

## 系统要求

- Microsoft Edge 浏览器（版本 88 或更高）
- Windows 10/11 或 macOS 10.14+ 或 Linux

## 安装步骤

### 方法一：开发者模式安装（推荐）

1. **下载扩展文件**
   - 下载整个项目文件夹到本地
   - 确保所有文件都在同一个文件夹中

2. **打开Edge扩展管理页面**
   - 打开Microsoft Edge浏览器
   - 在地址栏输入：`edge://extensions/`
   - 或者点击菜单 → 扩展

3. **启用开发者模式**
   - 在扩展页面右上角找到"开发人员模式"开关
   - 点击开关启用开发者模式

4. **加载扩展**
   - 点击"加载解压缩的扩展"按钮
   - 选择包含扩展文件的文件夹
   - 点击"选择文件夹"

5. **验证安装**
   - 扩展应该出现在扩展列表中
   - 浏览器工具栏应该显示扩展图标
   - 点击图标测试扩展是否正常工作

### 方法二：打包安装

1. **打包扩展**
   - 在扩展管理页面点击"打包扩展程序"
   - 选择扩展文件夹
   - 生成.crx文件

2. **安装打包文件**
   - 将.crx文件拖拽到扩展页面
   - 确认安装

## 配置说明

### 首次使用配置

1. **上传简历**
   - 点击扩展图标
   - 点击"上传简历"按钮
   - 选择DOC或DOCX格式的简历文件

2. **基本设置**
   - 右键点击扩展图标选择"选项"
   - 在设置页面配置基本选项：
     - 启用自动填写功能
     - 启用字段高亮
     - 设置默认简历

3. **AI功能配置（可选）**
   - 在设置页面的"AI设置"标签
   - 启用AI增强功能
   - 配置API密钥和端点（如果有）

## 权限说明

扩展需要以下权限：

- **storage**：用于在本地存储简历数据和设置
- **activeTab**：用于访问当前标签页内容
- **scripting**：用于在网页中注入脚本进行表单检测

这些权限都是必需的，用于扩展的核心功能。

## 故障排除

### 常见问题

**问题1：扩展无法加载**
- 检查文件夹结构是否完整
- 确保manifest.json文件存在且格式正确
- 检查是否启用了开发者模式

**问题2：扩展图标不显示**
- 检查图标文件是否存在
- 尝试刷新扩展页面
- 重新加载扩展

**问题3：无法上传简历**
- 检查文件格式是否为DOC或DOCX
- 确保文件大小不超过5MB
- 检查浏览器控制台是否有错误信息

**问题4：表单检测不工作**
- 刷新页面后重试
- 检查网页是否有表单元素
- 查看浏览器控制台错误信息

**问题5：自动填写失败**
- 确保已上传简历且信息完整
- 检查表单字段是否被正确识别
- 尝试手动填写部分字段

### 调试方法

1. **查看控制台错误**
   - 按F12打开开发者工具
   - 查看Console标签页的错误信息

2. **检查扩展状态**
   - 在扩展管理页面查看扩展状态
   - 点击"检查视图"查看后台页面

3. **重新加载扩展**
   - 在扩展管理页面点击刷新按钮
   - 或者移除后重新加载扩展

## 卸载方法

1. **移除扩展**
   - 打开扩展管理页面
   - 找到"简历自动填写助手"
   - 点击"移除"按钮

2. **清理数据**
   - 扩展移除后，本地数据会自动清理
   - 如需手动清理，可在设置页面点击"清除所有数据"

## 更新方法

1. **手动更新**
   - 下载新版本文件
   - 在扩展管理页面点击刷新按钮
   - 或者移除旧版本后重新安装

2. **检查更新**
   - 在扩展管理页面点击"更新"按钮
   - 系统会自动检查并安装更新

## 技术支持

如果遇到安装或使用问题：

1. 查看帮助文档：点击扩展图标 → 帮助
2. 检查README.md文件中的详细说明
3. 查看浏览器控制台的错误信息
4. 联系技术支持

## 注意事项

- 请确保从可信来源下载扩展文件
- 定期备份简历数据
- 保护好AI服务的API密钥
- 在公共电脑上使用后记得清除数据

---

安装完成后，您就可以开始使用简历自动填写助手了！
