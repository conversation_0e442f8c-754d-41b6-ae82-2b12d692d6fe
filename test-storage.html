<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>存储测试</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }
    .section {
      margin: 20px 0;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    button {
      padding: 10px 15px;
      margin: 5px;
      border: none;
      border-radius: 3px;
      background: #007cba;
      color: white;
      cursor: pointer;
    }
    button:hover {
      background: #005a87;
    }
    pre {
      background: #f5f5f5;
      padding: 10px;
      border-radius: 3px;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <h1>简历存储测试</h1>
  
  <div class="section">
    <h2>存储操作</h2>
    <button onclick="createTestResume()">创建测试简历</button>
    <button onclick="loadAllResumes()">加载所有简历</button>
    <button onclick="clearAllData()">清除所有数据</button>
  </div>
  
  <div class="section">
    <h2>存储内容</h2>
    <pre id="storage-content">点击"加载所有简历"查看存储内容</pre>
  </div>
  
  <div class="section">
    <h2>日志</h2>
    <pre id="log-content"></pre>
  </div>

  <script src="lib/utils.js"></script>
  <script src="lib/resume-manager.js"></script>
  <script>
    const resumeManager = new ResumeManager();
    
    function log(message) {
      const logElement = document.getElementById('log-content');
      const timestamp = new Date().toLocaleTimeString();
      logElement.textContent += `[${timestamp}] ${message}\n`;
      console.log(message);
    }
    
    async function createTestResume() {
      try {
        log('开始创建测试简历...');
        
        const testResumeData = {
          personalInfo: {
            fullName: '测试用户',
            email: '<EMAIL>',
            phone: '13800138000',
            address: {
              full: '北京市朝阳区',
              city: '北京',
              country: '中国'
            }
          },
          workExperience: [{
            company: '测试公司',
            position: '软件工程师',
            startDate: '2020-01-01',
            endDate: '2023-12-31',
            description: '负责前端开发工作'
          }],
          education: [{
            institution: '测试大学',
            degree: '学士',
            major: '计算机科学',
            graduationDate: '2020-06-30'
          }],
          skills: ['JavaScript', 'HTML', 'CSS']
        };
        
        const resumeId = await resumeManager.createResume(testResumeData, '测试简历');
        log(`测试简历创建成功，ID: ${resumeId}`);
        
        // 自动加载最新数据
        await loadAllResumes();
        
      } catch (error) {
        log(`创建测试简历失败: ${error.message}`);
      }
    }
    
    async function loadAllResumes() {
      try {
        log('开始加载所有简历...');
        
        const resumes = await resumeManager.getAllResumes();
        const resumeList = await resumeManager.getResumeList();
        
        log(`找到 ${Object.keys(resumes).length} 个简历`);
        log(`简历列表包含 ${resumeList.length} 项`);
        
        document.getElementById('storage-content').textContent = JSON.stringify({
          rawData: resumes,
          resumeList: resumeList
        }, null, 2);
        
      } catch (error) {
        log(`加载简历失败: ${error.message}`);
      }
    }
    
    async function clearAllData() {
      try {
        log('开始清除所有数据...');
        
        await resumeManager.clearAllData();
        log('所有数据已清除');
        
        document.getElementById('storage-content').textContent = '数据已清除';
        
      } catch (error) {
        log(`清除数据失败: ${error.message}`);
      }
    }
    
    // 监听存储变化
    chrome.storage.onChanged.addListener((changes, namespace) => {
      if (namespace === 'local') {
        log(`存储发生变化: ${Object.keys(changes).join(', ')}`);
        if (changes.resumes) {
          log('简历数据已更新');
        }
      }
    });
    
    // 页面加载时自动加载数据
    window.addEventListener('load', () => {
      log('页面加载完成');
      loadAllResumes();
    });
  </script>
</body>
</html>
