<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>简历自动填写助手 - 帮助文档</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background: #f8f9fa;
    }
    .container {
      background: white;
      padding: 40px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    h1 {
      color: #667eea;
      border-bottom: 2px solid #667eea;
      padding-bottom: 10px;
    }
    h2 {
      color: #333;
      margin-top: 30px;
    }
    .feature {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 5px;
      margin: 10px 0;
    }
    .step {
      background: #e3f2fd;
      padding: 15px;
      border-left: 4px solid #2196F3;
      margin: 10px 0;
    }
    code {
      background: #f1f1f1;
      padding: 2px 6px;
      border-radius: 3px;
      font-family: 'Courier New', monospace;
    }
    .warning {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      padding: 15px;
      border-radius: 5px;
      margin: 15px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>简历自动填写助手 - 帮助文档</h1>
    
    <h2>功能介绍</h2>
    <p>简历自动填写助手是一个智能的浏览器扩展，帮助您快速、准确地填写求职申请表单。</p>
    
    <div class="feature">
      <h3>主要功能</h3>
      <ul>
        <li><strong>DOC文件解析</strong>：支持上传DOC/DOCX格式的简历文件，自动提取个人信息</li>
        <li><strong>多简历管理</strong>：可以保存和管理多个简历档案，适用于不同类型的职位申请</li>
        <li><strong>智能表单检测</strong>：自动识别网页中的表单字段，判断字段用途</li>
        <li><strong>一键自动填写</strong>：将简历信息智能匹配到表单字段并自动填写</li>
        <li><strong>AI增强功能</strong>：可选的AI服务提供更准确的字段识别和内容匹配</li>
        <li><strong>数据安全</strong>：所有数据仅存储在本地，保护您的隐私</li>
      </ul>
    </div>

    <h2>使用指南</h2>
    
    <h3>1. 添加简历</h3>

    <h4>方法一：上传简历文件</h4>
    <div class="step">
      <p><strong>步骤1</strong>：点击扩展图标打开弹窗，或者右键点击扩展图标选择"选项"</p>
      <p><strong>步骤2</strong>：点击"上传简历"按钮，选择您的DOC或DOCX格式简历文件</p>
      <p><strong>步骤3</strong>：系统会自动解析文件并提取个人信息、工作经验、教育背景等</p>
      <p><strong>步骤4</strong>：检查解析结果，如有需要可以手动编辑和完善信息</p>
    </div>

    <h4>方法二：手动创建简历</h4>
    <div class="step">
      <p><strong>步骤1</strong>：点击扩展图标打开弹窗，或者进入选项页面</p>
      <p><strong>步骤2</strong>：点击"创建简历"按钮，打开简历编辑器</p>
      <p><strong>步骤3</strong>：填写基本信息：姓名、邮箱、电话、地址等</p>
      <p><strong>步骤4</strong>：添加工作经验：公司、职位、时间、工作描述</p>
      <p><strong>步骤5</strong>：添加教育背景：学校、专业、学位、毕业时间</p>
      <p><strong>步骤6</strong>：填写技能列表，用逗号分隔不同技能</p>
      <p><strong>步骤7</strong>：设置简历名称，点击"保存简历"</p>
    </div>

    <h3>2. 自动填写表单</h3>
    <div class="step">
      <p><strong>步骤1</strong>：打开求职申请网页</p>
      <p><strong>步骤2</strong>：点击扩展图标，选择要使用的简历档案</p>
      <p><strong>步骤3</strong>：点击"检测表单"按钮，系统会分析页面中的表单字段</p>
      <p><strong>步骤4</strong>：点击"自动填写"按钮，系统会将简历信息填入对应字段</p>
      <p><strong>步骤5</strong>：检查填写结果，手动调整不准确的内容</p>
    </div>

    <h3>3. 管理简历档案</h3>
    <div class="step">
      <p><strong>查看简历</strong>：在选项页面可以查看所有已保存的简历档案</p>
      <p><strong>编辑简历</strong>：点击编辑按钮打开简历编辑器，可以修改所有信息</p>
      <p><strong>复制简历</strong>：可以复制现有简历创建新的版本</p>
      <p><strong>删除简历</strong>：不需要的简历可以删除以节省存储空间</p>
      <p><strong>导出简历</strong>：可以将简历数据导出为JSON文件进行备份</p>
    </div>

    <h2>高级功能</h2>
    
    <h3>AI增强功能</h3>
    <p>启用AI功能可以提供更准确的字段识别和内容匹配：</p>
    <ul>
      <li>在设置页面启用"AI增强功能"</li>
      <li>配置您的AI服务API密钥和端点</li>
      <li>AI服务会分析页面内容，提供更智能的字段匹配</li>
    </ul>

    <div class="warning">
      <strong>注意</strong>：AI功能需要网络连接和有效的API密钥。如果AI服务不可用，系统会自动降级到基本模式。
    </div>

    <h3>数据管理</h3>
    <ul>
      <li><strong>导出数据</strong>：可以导出所有简历和设置数据进行备份</li>
      <li><strong>导入数据</strong>：可以从备份文件恢复数据</li>
      <li><strong>清除数据</strong>：可以清除所有本地数据（不可恢复）</li>
    </ul>

    <h2>常见问题</h2>
    
    <h3>Q: 支持哪些文件格式？</h3>
    <p>A: 目前支持DOC和DOCX格式的简历文件。文件大小限制为5MB。</p>

    <h3>Q: 数据存储在哪里？</h3>
    <p>A: 所有数据都存储在您的浏览器本地存储中，不会上传到任何服务器。</p>

    <h3>Q: 为什么有些字段没有被正确识别？</h3>
    <p>A: 字段识别基于常见的表单模式。如果遇到特殊的表单结构，可以尝试启用AI功能或手动填写。</p>

    <h3>Q: 如何提高填写准确性？</h3>
    <p>A: 
      <ul>
        <li>确保简历信息完整准确</li>
        <li>使用标准的简历格式</li>
        <li>启用AI增强功能</li>
        <li>在填写后检查并手动调整</li>
      </ul>
    </p>

    <h3>Q: 扩展不工作怎么办？</h3>
    <p>A: 
      <ul>
        <li>检查扩展是否已启用</li>
        <li>刷新页面后重试</li>
        <li>检查浏览器控制台是否有错误信息</li>
        <li>尝试重新安装扩展</li>
      </ul>
    </p>

    <h2>隐私政策</h2>
    <p>我们非常重视您的隐私：</p>
    <ul>
      <li>所有简历数据仅存储在您的本地设备上</li>
      <li>扩展不会收集或上传您的个人信息</li>
      <li>AI功能是可选的，您可以随时禁用</li>
      <li>使用AI功能时，数据会进行脱敏处理</li>
    </ul>

    <h2>技术支持</h2>
    <p>如果您在使用过程中遇到问题，请：</p>
    <ul>
      <li>查看浏览器控制台的错误信息</li>
      <li>尝试禁用其他可能冲突的扩展</li>
      <li>确保浏览器版本是最新的</li>
      <li>联系技术支持获取帮助</li>
    </ul>

    <p style="text-align: center; margin-top: 40px; color: #666;">
      简历自动填写助手 v1.0.0<br>
      © 2024 版权所有
    </p>
  </div>
</body>
</html>
