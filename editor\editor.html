<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>简历编辑器</title>
  <link rel="stylesheet" href="editor.css">
</head>
<body>
  <div class="editor-container">
    <!-- 头部 -->
    <div class="editor-header">
      <h1>简历编辑器</h1>
      <div class="header-actions">
        <button id="save-btn" class="btn btn-primary">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z"/>
          </svg>
          保存简历
        </button>
        <button id="close-btn" class="btn btn-secondary">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
          </svg>
          关闭
        </button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="editor-content">
      <form id="resume-form">
        <!-- 基本信息部分 -->
        <div class="form-section">
          <h2>基本信息</h2>
          
          <div class="form-row">
            <div class="form-group">
              <label for="fullName">姓名 <span class="required">*</span></label>
              <input type="text" id="fullName" name="fullName" required>
            </div>
            
            <div class="form-group">
              <label for="email">邮箱 <span class="required">*</span></label>
              <input type="email" id="email" name="email" required>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="phone">电话 <span class="required">*</span></label>
              <input type="tel" id="phone" name="phone" required>
            </div>
            
            <div class="form-group">
              <label for="address">地址</label>
              <input type="text" id="address" name="address">
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="city">城市</label>
              <input type="text" id="city" name="city">
            </div>
            
            <div class="form-group">
              <label for="linkedIn">LinkedIn</label>
              <input type="url" id="linkedIn" name="linkedIn" placeholder="https://linkedin.com/in/yourname">
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="website">个人网站</label>
              <input type="url" id="website" name="website" placeholder="https://yourwebsite.com">
            </div>
          </div>
        </div>
        
        <!-- 工作经验部分 -->
        <div class="form-section">
          <h2>工作经验</h2>
          <div id="work-experience-container">
            <!-- 工作经验项模板 -->
            <div class="experience-item" data-index="0">
              <div class="form-row">
                <div class="form-group">
                  <label for="company-0">公司名称 <span class="required">*</span></label>
                  <input type="text" id="company-0" name="company-0" required>
                </div>
                
                <div class="form-group">
                  <label for="position-0">职位 <span class="required">*</span></label>
                  <input type="text" id="position-0" name="position-0" required>
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label for="startDate-0">开始日期</label>
                  <input type="date" id="startDate-0" name="startDate-0">
                </div>
                
                <div class="form-group">
                  <label for="endDate-0">结束日期</label>
                  <input type="date" id="endDate-0" name="endDate-0">
                  <div class="checkbox-group">
                    <input type="checkbox" id="current-0" name="current-0">
                    <label for="current-0">至今</label>
                  </div>
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group full-width">
                  <label for="description-0">工作描述</label>
                  <textarea id="description-0" name="description-0" rows="3"></textarea>
                </div>
              </div>
              
              <div class="item-actions">
                <button type="button" class="btn btn-danger remove-item" data-target="experience" data-index="0">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
                  </svg>
                  删除
                </button>
              </div>
            </div>
          </div>
          
          <button type="button" id="add-experience" class="btn btn-secondary add-item">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
            </svg>
            添加工作经验
          </button>
        </div>
        
        <!-- 教育背景部分 -->
        <div class="form-section">
          <h2>教育背景</h2>
          <div id="education-container">
            <!-- 教育背景项模板 -->
            <div class="education-item" data-index="0">
              <div class="form-row">
                <div class="form-group">
                  <label for="institution-0">学校名称 <span class="required">*</span></label>
                  <input type="text" id="institution-0" name="institution-0" required>
                </div>
                
                <div class="form-group">
                  <label for="degree-0">学位</label>
                  <input type="text" id="degree-0" name="degree-0">
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label for="major-0">专业 <span class="required">*</span></label>
                  <input type="text" id="major-0" name="major-0" required>
                </div>
                
                <div class="form-group">
                  <label for="graduationDate-0">毕业日期</label>
                  <input type="date" id="graduationDate-0" name="graduationDate-0">
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label for="gpa-0">GPA</label>
                  <input type="number" id="gpa-0" name="gpa-0" step="0.01" min="0" max="4">
                </div>
              </div>
              
              <div class="item-actions">
                <button type="button" class="btn btn-danger remove-item" data-target="education" data-index="0">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
                  </svg>
                  删除
                </button>
              </div>
            </div>
          </div>
          
          <button type="button" id="add-education" class="btn btn-secondary add-item">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
            </svg>
            添加教育背景
          </button>
        </div>
        
        <!-- 技能部分 -->
        <div class="form-section">
          <h2>技能</h2>
          <div class="form-row">
            <div class="form-group full-width">
              <label for="skills">技能列表 (用逗号分隔)</label>
              <textarea id="skills" name="skills" rows="3" placeholder="例如: JavaScript, HTML, CSS, React, Node.js"></textarea>
            </div>
          </div>
        </div>
        
        <!-- 简历名称 -->
        <div class="form-section">
          <h2>简历信息</h2>
          <div class="form-row">
            <div class="form-group full-width">
              <label for="resumeName">简历名称 <span class="required">*</span></label>
              <input type="text" id="resumeName" name="resumeName" required placeholder="例如: 前端开发简历">
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- 底部操作 -->
    <div class="editor-footer">
      <button id="cancel-btn" class="btn btn-secondary">取消</button>
      <button id="save-resume-btn" class="btn btn-primary">保存简历</button>
    </div>
  </div>

  <!-- 通知容器 -->
  <div id="notification-container" class="notification-container"></div>

  <!-- 脚本 -->
  <script src="../lib/utils.js"></script>
  <script src="../lib/resume-manager.js"></script>
  <script src="editor.js"></script>
</body>
</html>
